import { motion } from 'framer-motion'
import { useState, useEffect } from 'react'
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Users, 
  Plus,
  ArrowUpRight,
  ArrowDownRight
} from 'lucide-react'
import { <PERSON>Chart, Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, Tooltip } from 'recharts'
import ExpenseCard from '../components/ui/ExpenseCard'
import { Link } from 'react-router-dom'

// Mock data
const mockExpenses = [
  {
    id: 1,
    title: 'Dinner at Italian Restaurant',
    description: 'Team dinner after project completion',
    amount: 240.50,
    date: '2024-01-15',
    category: 'food',
    participants: [
      { name: 'You', amount: 60.13 },
      { name: '<PERSON>', amount: 60.13 },
      { name: '<PERSON>', amount: 60.12 },
      { name: '<PERSON>', amount: 60.12 }
    ],
    yourShare: 60.13
  },
  {
    id: 2,
    title: 'Uber to Airport',
    description: 'Shared ride for vacation trip',
    amount: 45.00,
    date: '2024-01-14',
    category: 'transport',
    participants: [
      { name: 'You', amount: 22.50 },
      { name: '<PERSON>', amount: 22.50 }
    ],
    yourShare: 22.50
  },
  {
    id: 3,
    title: 'Movie Night',
    description: 'Tickets and snacks for group movie',
    amount: 85.75,
    date: '2024-01-12',
    category: 'entertainment',
    participants: [
      { name: 'You', amount: 28.58 },
      { name: 'Mike', amount: 28.58 },
      { name: 'Lisa', amount: 28.59 }
    ],
    yourShare: 28.58
  }
]

const categoryData = [
  { name: 'Food', value: 240.50, color: '#f59e0b' },
  { name: 'Transport', value: 45.00, color: '#3b82f6' },
  { name: 'Entertainment', value: 85.75, color: '#8b5cf6' },
  { name: 'Shopping', value: 120.25, color: '#10b981' }
]

const monthlyData = [
  { month: 'Jan', amount: 450 },
  { month: 'Feb', amount: 320 },
  { month: 'Mar', amount: 580 },
  { month: 'Apr', amount: 290 },
  { month: 'May', amount: 670 },
  { month: 'Jun', amount: 491 }
]

function Dashboard() {
  const [totalExpenses, setTotalExpenses] = useState(0)
  const [yourShare, setYourShare] = useState(0)
  const [activeUsers, setActiveUsers] = useState(0)

  useEffect(() => {
    // Calculate totals
    const total = mockExpenses.reduce((sum, expense) => sum + expense.amount, 0)
    const share = mockExpenses.reduce((sum, expense) => sum + expense.yourShare, 0)
    
    setTotalExpenses(total)
    setYourShare(share)
    setActiveUsers(8) // Mock active users
  }, [])

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  return (
    <div className="min-h-screen pt-24 pb-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
              <h1 className="text-3xl md:text-4xl font-bold gradient-text mb-2">
                Dashboard
              </h1>
              <p className="text-slate-600 dark:text-slate-400">
                Track your expenses and manage splits with ease
              </p>
            </div>
            <Link to="/add-expense">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="mt-4 md:mt-0 px-6 py-3 bg-gradient-to-r from-primary-500 to-primary-600 text-white rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 flex items-center space-x-2"
              >
                <Plus size={20} />
                <span>Add Expense</span>
              </motion.button>
            </Link>
          </div>
        </motion.div>

        {/* Stats Cards */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8"
        >
          <div className="glass rounded-2xl p-6 hover:glass-strong transition-all duration-300">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 rounded-xl bg-gradient-to-r from-green-400 to-emerald-500 text-white">
                <DollarSign size={24} />
              </div>
              <div className="flex items-center text-green-500 text-sm">
                <ArrowUpRight size={16} />
                <span>+12%</span>
              </div>
            </div>
            <div className="text-2xl font-bold text-slate-800 dark:text-slate-200 mb-1">
              {formatCurrency(totalExpenses)}
            </div>
            <div className="text-slate-600 dark:text-slate-400 text-sm">
              Total Expenses
            </div>
          </div>

          <div className="glass rounded-2xl p-6 hover:glass-strong transition-all duration-300">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 rounded-xl bg-gradient-to-r from-blue-400 to-indigo-500 text-white">
                <TrendingUp size={24} />
              </div>
              <div className="flex items-center text-red-500 text-sm">
                <ArrowDownRight size={16} />
                <span>-5%</span>
              </div>
            </div>
            <div className="text-2xl font-bold text-slate-800 dark:text-slate-200 mb-1">
              {formatCurrency(yourShare)}
            </div>
            <div className="text-slate-600 dark:text-slate-400 text-sm">
              Your Share
            </div>
          </div>

          <div className="glass rounded-2xl p-6 hover:glass-strong transition-all duration-300">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 rounded-xl bg-gradient-to-r from-purple-400 to-pink-500 text-white">
                <Users size={24} />
              </div>
              <div className="flex items-center text-green-500 text-sm">
                <ArrowUpRight size={16} />
                <span>+3</span>
              </div>
            </div>
            <div className="text-2xl font-bold text-slate-800 dark:text-slate-200 mb-1">
              {activeUsers}
            </div>
            <div className="text-slate-600 dark:text-slate-400 text-sm">
              Active Friends
            </div>
          </div>
        </motion.div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Pie Chart */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="glass rounded-2xl p-6"
          >
            <h3 className="text-xl font-semibold text-slate-800 dark:text-slate-200 mb-6">
              Expenses by Category
            </h3>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={categoryData}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={100}
                    paddingAngle={5}
                    dataKey="value"
                  >
                    {categoryData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => formatCurrency(value)} />
                </PieChart>
              </ResponsiveContainer>
            </div>
            <div className="grid grid-cols-2 gap-4 mt-4">
              {categoryData.map((category, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <div 
                    className="w-3 h-3 rounded-full" 
                    style={{ backgroundColor: category.color }}
                  />
                  <span className="text-sm text-slate-600 dark:text-slate-400">
                    {category.name}
                  </span>
                </div>
              ))}
            </div>
          </motion.div>

          {/* Bar Chart */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="glass rounded-2xl p-6"
          >
            <h3 className="text-xl font-semibold text-slate-800 dark:text-slate-200 mb-6">
              Monthly Spending
            </h3>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={monthlyData}>
                  <XAxis 
                    dataKey="month" 
                    axisLine={false}
                    tickLine={false}
                    tick={{ fill: '#64748b', fontSize: 12 }}
                  />
                  <YAxis 
                    axisLine={false}
                    tickLine={false}
                    tick={{ fill: '#64748b', fontSize: 12 }}
                  />
                  <Tooltip 
                    formatter={(value) => formatCurrency(value)}
                    contentStyle={{
                      backgroundColor: 'rgba(255, 255, 255, 0.9)',
                      border: 'none',
                      borderRadius: '12px',
                      boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)'
                    }}
                  />
                  <Bar 
                    dataKey="amount" 
                    fill="url(#gradient)"
                    radius={[4, 4, 0, 0]}
                  />
                  <defs>
                    <linearGradient id="gradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="0%" stopColor="#22c55e" />
                      <stop offset="100%" stopColor="#16a34a" />
                    </linearGradient>
                  </defs>
                </BarChart>
              </ResponsiveContainer>
            </div>
          </motion.div>
        </div>

        {/* Recent Expenses */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-slate-800 dark:text-slate-200">
              Recent Expenses
            </h2>
            <Link 
              to="/history"
              className="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium transition-colors duration-200"
            >
              View All
            </Link>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {mockExpenses.map((expense, index) => (
              <motion.div
                key={expense.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: 0.5 + index * 0.1 }}
              >
                <ExpenseCard 
                  expense={expense}
                  onEdit={(expense) => console.log('Edit:', expense)}
                  onDelete={(expense) => console.log('Delete:', expense)}
                  onView={(expense) => console.log('View:', expense)}
                />
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </div>
  )
}

export default Dashboard
