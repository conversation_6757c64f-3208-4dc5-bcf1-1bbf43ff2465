import { motion } from 'framer-motion'
import { useState, useEffect } from 'react'
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  Users,
  PlusCircle,
  ArrowUpRight,
  ArrowDownRight,
  Wallet,
  CreditCard,
  PieChart as PieChartIcon,
  BarChart3,
  Calendar,
  Filter,
  Download,
  Eye,
  Sparkles,
  Target,
  Activity
} from 'lucide-react'
import { PieChart, Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, Tooltip } from 'recharts'
import ExpenseCard from '../components/ui/ExpenseCard'
import AppNavbar from '../components/ui/AppNavbar'
import { Link } from 'react-router-dom'

// Mock data
const mockExpenses = [
  {
    id: 1,
    title: 'Dinner at Italian Restaurant',
    description: 'Team dinner after project completion',
    amount: 240.50,
    date: '2024-01-15',
    category: 'food',
    participants: [
      { name: 'You', amount: 60.13 },
      { name: '<PERSON>', amount: 60.13 },
      { name: '<PERSON>', amount: 60.12 },
      { name: '<PERSON>', amount: 60.12 }
    ],
    yourShare: 60.13
  },
  {
    id: 2,
    title: 'Uber to Airport',
    description: 'Shared ride for vacation trip',
    amount: 45.00,
    date: '2024-01-14',
    category: 'transport',
    participants: [
      { name: 'You', amount: 22.50 },
      { name: '<PERSON>', amount: 22.50 }
    ],
    yourShare: 22.50
  },
  {
    id: 3,
    title: 'Movie Night',
    description: 'Tickets and snacks for group movie',
    amount: 85.75,
    date: '2024-01-12',
    category: 'entertainment',
    participants: [
      { name: 'You', amount: 28.58 },
      { name: 'Mike', amount: 28.58 },
      { name: 'Lisa', amount: 28.59 }
    ],
    yourShare: 28.58
  }
]

const categoryData = [
  { name: 'Food', value: 240.50, color: '#f59e0b' },
  { name: 'Transport', value: 45.00, color: '#3b82f6' },
  { name: 'Entertainment', value: 85.75, color: '#8b5cf6' },
  { name: 'Shopping', value: 120.25, color: '#10b981' }
]

const monthlyData = [
  { month: 'Jan', amount: 450 },
  { month: 'Feb', amount: 320 },
  { month: 'Mar', amount: 580 },
  { month: 'Apr', amount: 290 },
  { month: 'May', amount: 670 },
  { month: 'Jun', amount: 491 }
]

function Dashboard() {
  const [totalExpenses, setTotalExpenses] = useState(0)
  const [yourShare, setYourShare] = useState(0)
  const [activeUsers, setActiveUsers] = useState(0)

  useEffect(() => {
    // Calculate totals
    const total = mockExpenses.reduce((sum, expense) => sum + expense.amount, 0)
    const share = mockExpenses.reduce((sum, expense) => sum + expense.yourShare, 0)
    
    setTotalExpenses(total)
    setYourShare(share)
    setActiveUsers(8) // Mock active users
  }, [])

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      <AppNavbar />
      <div className="pt-8 pb-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Enhanced Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="mb-12"
          >
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
              <div className="mb-6 lg:mb-0">
                <motion.h1
                  className="text-4xl lg:text-5xl font-black text-gray-900 mb-3"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.1 }}
                >
                  Hello, <span className="bg-gradient-to-r from-pink-500 via-purple-500 to-indigo-500 bg-clip-text text-transparent">David</span>
                  <motion.span
                    animate={{ rotate: [0, 14, -8, 14, -4, 10, 0] }}
                    transition={{ duration: 2.5, repeat: Infinity, repeatDelay: 3 }}
                    className="inline-block ml-2"
                  >
                    👋
                  </motion.span>
                </motion.h1>
                <motion.p
                  className="text-xl text-gray-600 font-medium"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                >
                  Track your expenses with <span className="text-purple-600 font-semibold">ease</span> ✨
                </motion.p>
              </div>

              <motion.div
                className="flex items-center space-x-4"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: 0.3 }}
              >
                <div className="w-16 h-16 bg-gradient-to-br from-pink-500 via-purple-500 to-indigo-500 rounded-2xl flex items-center justify-center shadow-lg shadow-purple-500/25">
                  <span className="text-white text-xl font-bold">D</span>
                </div>
                <Link to="/add-expense">
                  <motion.button
                    whileHover={{ scale: 1.05, y: -2 }}
                    whileTap={{ scale: 0.95 }}
                    className="flex items-center space-x-3 px-6 py-4 bg-gradient-to-r from-pink-500 to-purple-600 text-white rounded-2xl font-bold shadow-lg shadow-purple-500/25 hover:shadow-xl hover:shadow-purple-500/30 transition-all duration-300"
                  >
                    <PlusCircle className="w-5 h-5" />
                    <span>Add Expense</span>
                  </motion.button>
                </Link>
              </motion.div>
            </div>

            {/* Enhanced Filter Tabs */}
            <motion.div
              className="flex flex-wrap gap-3"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              {[
                { label: 'All', icon: Activity, active: true },
                { label: 'Daily', icon: Calendar, active: false },
                { label: 'Weekly', icon: BarChart3, active: false },
                { label: 'Monthly', icon: PieChartIcon, active: false }
              ].map((tab, index) => {
                const Icon = tab.icon
                return (
                  <motion.button
                    key={tab.label}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className={`flex items-center space-x-2 px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${
                      tab.active
                        ? 'bg-gradient-to-r from-pink-500 to-purple-600 text-white shadow-lg shadow-purple-500/25'
                        : 'bg-white/80 backdrop-blur-sm text-gray-700 hover:bg-white hover:shadow-md border border-gray-200/50'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span>{tab.label}</span>
                  </motion.button>
                )
              })}
            </motion.div>
          </motion.div>

          {/* Enhanced Stats Cards */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12"
          >
            {/* Total Balance Card */}
            <motion.div
              className="bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl p-6 text-white shadow-lg shadow-emerald-500/25"
              whileHover={{ scale: 1.02, y: -5 }}
              transition={{ duration: 0.3 }}
            >
              <div className="flex items-center justify-between mb-4">
                <Wallet className="w-8 h-8 text-emerald-100" />
                <TrendingUp className="w-5 h-5 text-emerald-200" />
              </div>
              <div className="text-3xl font-bold mb-1">$8,429</div>
              <div className="text-emerald-100 text-sm font-medium">Total Balance</div>
            </motion.div>

            {/* Spent Card */}
            <motion.div
              className="bg-gradient-to-br from-red-500 to-pink-600 rounded-2xl p-6 text-white shadow-lg shadow-red-500/25"
              whileHover={{ scale: 1.02, y: -5 }}
              transition={{ duration: 0.3 }}
            >
              <div className="flex items-center justify-between mb-4">
                <CreditCard className="w-8 h-8 text-red-100" />
                <TrendingDown className="w-5 h-5 text-red-200" />
              </div>
              <div className="text-3xl font-bold mb-1">$3,621</div>
              <div className="text-red-100 text-sm font-medium">Total Spent</div>
            </motion.div>

            {/* Groups Card */}
            <motion.div
              className="bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl p-6 text-white shadow-lg shadow-blue-500/25"
              whileHover={{ scale: 1.02, y: -5 }}
              transition={{ duration: 0.3 }}
            >
              <div className="flex items-center justify-between mb-4">
                <Users className="w-8 h-8 text-blue-100" />
                <ArrowUpRight className="w-5 h-5 text-blue-200" />
              </div>
              <div className="text-3xl font-bold mb-1">12</div>
              <div className="text-blue-100 text-sm font-medium">Active Groups</div>
            </motion.div>

            {/* Savings Card */}
            <motion.div
              className="bg-gradient-to-br from-purple-500 to-violet-600 rounded-2xl p-6 text-white shadow-lg shadow-purple-500/25"
              whileHover={{ scale: 1.02, y: -5 }}
              transition={{ duration: 0.3 }}
            >
              <div className="flex items-center justify-between mb-4">
                <Target className="w-8 h-8 text-purple-100" />
                <Sparkles className="w-5 h-5 text-purple-200" />
              </div>
              <div className="text-3xl font-bold mb-1">$4,808</div>
              <div className="text-purple-100 text-sm font-medium">Savings Goal</div>
            </motion.div>
          </motion.div>

        {/* Donut Chart */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="premium-card p-6 mb-8"
        >
          <div className="flex items-center justify-center">
            <div className="relative w-32 h-32">
              <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 36 36">
                <path
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  fill="none"
                  stroke="#f1f5f9"
                  strokeWidth="3"
                />
                <path
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  fill="none"
                  stroke="#10b981"
                  strokeWidth="3"
                  strokeDasharray="70, 100"
                />
                <path
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  fill="none"
                  stroke="#f87171"
                  strokeWidth="3"
                  strokeDasharray="30, 100"
                  strokeDashoffset="-70"
                />
              </svg>
            </div>
          </div>
        </motion.div>

        {/* Recent Transactions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-dark">Recent transactions</h3>
            <button className="text-emerald-500 text-sm font-medium">See All</button>
          </div>

          <div className="space-y-3">
            {/* Food Transaction */}
            <div className="expense-card p-4 flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
                  <span className="text-lg">🍽️</span>
                </div>
                <div>
                  <div className="font-medium text-dark">Food</div>
                  <div className="text-sm text-soft">Card</div>
                </div>
              </div>
              <div className="text-right">
                <div className="font-semibold text-dark">-$12</div>
                <div className="text-sm text-soft">Mar 07, 2023</div>
              </div>
            </div>

            {/* Salary Transaction */}
            <div className="expense-card p-4 flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-emerald-100 rounded-full flex items-center justify-center">
                  <span className="text-lg">💰</span>
                </div>
                <div>
                  <div className="font-medium text-dark">Salary</div>
                  <div className="text-sm text-soft">Bank Account</div>
                </div>
              </div>
              <div className="text-right">
                <div className="font-semibold text-emerald-600">+$6800</div>
                <div className="text-sm text-soft">Mar 07, 2023</div>
              </div>
            </div>

            {/* Entertainment Transaction */}
            <div className="expense-card p-4 flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                  <span className="text-lg">🎬</span>
                </div>
                <div>
                  <div className="font-medium text-dark">Entertainment</div>
                  <div className="text-sm text-soft">Card</div>
                </div>
              </div>
              <div className="text-right">
                <div className="font-semibold text-dark">-$8</div>
                <div className="text-sm text-soft">Mar 07, 2023</div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Add Expense Button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="fixed bottom-24 right-6"
        >
          <Link to="/add-expense">
            <button className="w-14 h-14 bg-gradient-to-br from-emerald-400 to-emerald-600 rounded-full shadow-strong flex items-center justify-center">
              <Plus size={24} className="text-white" />
            </button>
          </Link>
        </motion.div>
      </div>
      </div>
    </div>
  )
}

export default Dashboard
