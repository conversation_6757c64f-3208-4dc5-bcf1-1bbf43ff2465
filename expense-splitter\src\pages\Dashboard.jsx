import { motion } from 'framer-motion'
import { useState, useEffect } from 'react'
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  Users,
  Plus,
  ArrowUpRight,
  ArrowDownRight
} from 'lucide-react'
import { <PERSON>Chart, Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, Tooltip } from 'recharts'
import ExpenseCard from '../components/ui/ExpenseCard'
import AppNavbar from '../components/ui/AppNavbar'
import { Link } from 'react-router-dom'

// Mock data
const mockExpenses = [
  {
    id: 1,
    title: 'Dinner at Italian Restaurant',
    description: 'Team dinner after project completion',
    amount: 240.50,
    date: '2024-01-15',
    category: 'food',
    participants: [
      { name: '<PERSON>', amount: 60.13 },
      { name: '<PERSON>', amount: 60.13 },
      { name: '<PERSON>', amount: 60.12 },
      { name: '<PERSON>', amount: 60.12 }
    ],
    yourShare: 60.13
  },
  {
    id: 2,
    title: 'Uber to Airport',
    description: 'Shared ride for vacation trip',
    amount: 45.00,
    date: '2024-01-14',
    category: 'transport',
    participants: [
      { name: 'You', amount: 22.50 },
      { name: '<PERSON>', amount: 22.50 }
    ],
    yourShare: 22.50
  },
  {
    id: 3,
    title: 'Movie Night',
    description: 'Tickets and snacks for group movie',
    amount: 85.75,
    date: '2024-01-12',
    category: 'entertainment',
    participants: [
      { name: 'You', amount: 28.58 },
      { name: 'Mike', amount: 28.58 },
      { name: 'Lisa', amount: 28.59 }
    ],
    yourShare: 28.58
  }
]

const categoryData = [
  { name: 'Food', value: 240.50, color: '#f59e0b' },
  { name: 'Transport', value: 45.00, color: '#3b82f6' },
  { name: 'Entertainment', value: 85.75, color: '#8b5cf6' },
  { name: 'Shopping', value: 120.25, color: '#10b981' }
]

const monthlyData = [
  { month: 'Jan', amount: 450 },
  { month: 'Feb', amount: 320 },
  { month: 'Mar', amount: 580 },
  { month: 'Apr', amount: 290 },
  { month: 'May', amount: 670 },
  { month: 'Jun', amount: 491 }
]

function Dashboard() {
  const [totalExpenses, setTotalExpenses] = useState(0)
  const [yourShare, setYourShare] = useState(0)
  const [activeUsers, setActiveUsers] = useState(0)

  useEffect(() => {
    // Calculate totals
    const total = mockExpenses.reduce((sum, expense) => sum + expense.amount, 0)
    const share = mockExpenses.reduce((sum, expense) => sum + expense.yourShare, 0)
    
    setTotalExpenses(total)
    setYourShare(share)
    setActiveUsers(8) // Mock active users
  }, [])

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      <AppNavbar />
      <div className="pt-8 pb-24">
      <div className="container-mobile">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-2xl font-bold text-dark mb-1">
                Hello, <span className="text-gradient">David</span> 👋
              </h1>
              <p className="text-soft text-sm">
                Track your expenses with ease
              </p>
            </div>
            <div className="w-10 h-10 bg-gradient-to-br from-emerald-400 to-emerald-600 rounded-full flex items-center justify-center">
              <span className="text-white text-sm font-semibold">D</span>
            </div>
          </div>

          {/* Filter Tabs */}
          <div className="flex space-x-2 mb-6">
            <button className="px-4 py-2 bg-white rounded-full text-sm font-medium text-dark shadow-soft">All</button>
            <button className="px-4 py-2 text-soft text-sm">Daily</button>
            <button className="px-4 py-2 text-soft text-sm">Weekly</button>
            <button className="px-4 py-2 text-soft text-sm">Monthly</button>
          </div>
        </motion.div>

        {/* Income/Spent Cards */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="grid grid-cols-2 gap-4 mb-8"
        >
          {/* Income Card */}
          <div className="premium-card p-6">
            <div className="flex items-center mb-3">
              <div className="w-2 h-2 bg-emerald-500 rounded-full mr-2"></div>
              <span className="text-soft text-sm">Income</span>
            </div>
            <div className="text-2xl font-bold text-dark">
              $8,429
            </div>
          </div>

          {/* Spent Card */}
          <div className="premium-card p-6">
            <div className="flex items-center mb-3">
              <div className="w-2 h-2 bg-red-400 rounded-full mr-2"></div>
              <span className="text-soft text-sm">Spent</span>
            </div>
            <div className="text-2xl font-bold text-dark">
              $3,621
            </div>
          </div>
        </motion.div>

        {/* Donut Chart */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="premium-card p-6 mb-8"
        >
          <div className="flex items-center justify-center">
            <div className="relative w-32 h-32">
              <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 36 36">
                <path
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  fill="none"
                  stroke="#f1f5f9"
                  strokeWidth="3"
                />
                <path
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  fill="none"
                  stroke="#10b981"
                  strokeWidth="3"
                  strokeDasharray="70, 100"
                />
                <path
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  fill="none"
                  stroke="#f87171"
                  strokeWidth="3"
                  strokeDasharray="30, 100"
                  strokeDashoffset="-70"
                />
              </svg>
            </div>
          </div>
        </motion.div>

        {/* Recent Transactions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-dark">Recent transactions</h3>
            <button className="text-emerald-500 text-sm font-medium">See All</button>
          </div>

          <div className="space-y-3">
            {/* Food Transaction */}
            <div className="expense-card p-4 flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
                  <span className="text-lg">🍽️</span>
                </div>
                <div>
                  <div className="font-medium text-dark">Food</div>
                  <div className="text-sm text-soft">Card</div>
                </div>
              </div>
              <div className="text-right">
                <div className="font-semibold text-dark">-$12</div>
                <div className="text-sm text-soft">Mar 07, 2023</div>
              </div>
            </div>

            {/* Salary Transaction */}
            <div className="expense-card p-4 flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-emerald-100 rounded-full flex items-center justify-center">
                  <span className="text-lg">💰</span>
                </div>
                <div>
                  <div className="font-medium text-dark">Salary</div>
                  <div className="text-sm text-soft">Bank Account</div>
                </div>
              </div>
              <div className="text-right">
                <div className="font-semibold text-emerald-600">+$6800</div>
                <div className="text-sm text-soft">Mar 07, 2023</div>
              </div>
            </div>

            {/* Entertainment Transaction */}
            <div className="expense-card p-4 flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                  <span className="text-lg">🎬</span>
                </div>
                <div>
                  <div className="font-medium text-dark">Entertainment</div>
                  <div className="text-sm text-soft">Card</div>
                </div>
              </div>
              <div className="text-right">
                <div className="font-semibold text-dark">-$8</div>
                <div className="text-sm text-soft">Mar 07, 2023</div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Add Expense Button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="fixed bottom-24 right-6"
        >
          <Link to="/add-expense">
            <button className="w-14 h-14 bg-gradient-to-br from-emerald-400 to-emerald-600 rounded-full shadow-strong flex items-center justify-center">
              <Plus size={24} className="text-white" />
            </button>
          </Link>
        </motion.div>
      </div>
    </div>
  )
}

export default Dashboard
