import { motion } from 'framer-motion'
import { Link, useLocation } from 'react-router-dom'
import {
  LayoutDashboard,
  PlusCircle,
  Clock,
  Menu,
  X,
  Sparkles,
  User,
  Settings,
  LogOut
} from 'lucide-react'
import { useState } from 'react'

function AppNavbar() {
  const location = useLocation()
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  const navItems = [
    { path: '/dashboard', label: 'Dashboard', icon: LayoutDashboard },
    { path: '/add-expense', label: 'Add Expense', icon: PlusCircle },
    { path: '/history', label: 'History', icon: Clock },
  ]

  return (
    <nav className="bg-gradient-to-r from-white/95 to-blue-50/95 backdrop-blur-xl border-b border-blue-200/30 sticky top-0 z-50 shadow-lg shadow-blue-100/20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-4">
          {/* Enhanced Brand */}
          <Link to="/" className="flex items-center group">
            <motion.div
              className="w-10 h-10 rounded-xl bg-gradient-to-br from-pink-500 via-purple-500 to-indigo-500 mr-3 flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300"
              whileHover={{ scale: 1.05, rotate: 5 }}
              whileTap={{ scale: 0.95 }}
            >
              <Sparkles className="w-5 h-5 text-white" />
            </motion.div>
            <span className="text-2xl font-bold bg-gradient-to-r from-pink-500 via-purple-500 to-indigo-500 bg-clip-text text-transparent">
              SplitWise
            </span>
          </Link>

          {/* Enhanced Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-2">
            {navItems.map((item) => {
              const Icon = item.icon
              const isActive = location.pathname === item.path
              return (
                <motion.div key={item.path} whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                  <Link
                    to={item.path}
                    className={`flex items-center space-x-3 px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${
                      isActive
                        ? 'bg-gradient-to-r from-pink-500 to-purple-600 text-white shadow-lg shadow-purple-500/25'
                        : 'text-gray-700 hover:text-gray-900 hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 hover:shadow-md'
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    <span>{item.label}</span>
                  </Link>
                </motion.div>
              )
            })}
          </div>

          {/* User Profile & Mobile Menu */}
          <div className="flex items-center space-x-4">
            {/* User Profile (Desktop) */}
            <div className="hidden lg:flex items-center space-x-3">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="flex items-center space-x-2 px-4 py-2 rounded-xl bg-gradient-to-r from-gray-100 to-gray-200 hover:from-gray-200 hover:to-gray-300 transition-all duration-300 shadow-md"
              >
                <User className="w-4 h-4 text-gray-600" />
                <span className="text-sm font-medium text-gray-700">David</span>
              </motion.button>

              <motion.button
                whileHover={{ scale: 1.05, rotate: 90 }}
                whileTap={{ scale: 0.95 }}
                className="p-3 rounded-xl bg-gradient-to-r from-gray-100 to-gray-200 hover:from-gray-200 hover:to-gray-300 transition-all duration-300 shadow-md"
              >
                <Settings className="w-4 h-4 text-gray-600" />
              </motion.button>
            </div>

            {/* Mobile menu button */}
            <motion.button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="lg:hidden p-3 rounded-xl bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg hover:shadow-xl transition-all duration-300"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {isMobileMenuOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
            </motion.button>
          </div>
        </div>

        {/* Enhanced Mobile menu */}
        {isMobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0, y: -20, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -20, scale: 0.95 }}
            className="lg:hidden py-6 border-t border-purple-200/50 bg-gradient-to-br from-white/95 to-purple-50/95 backdrop-blur-xl rounded-b-2xl"
          >
            <div className="flex flex-col space-y-3">
              {navItems.map((item, index) => {
                const Icon = item.icon
                const isActive = location.pathname === item.path
                return (
                  <motion.div
                    key={item.path}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <Link
                      to={item.path}
                      onClick={() => setIsMobileMenuOpen(false)}
                      className={`flex items-center space-x-4 px-6 py-4 rounded-xl font-semibold transition-all duration-300 ${
                        isActive
                          ? 'bg-gradient-to-r from-pink-500 to-purple-600 text-white shadow-lg'
                          : 'text-gray-700 hover:text-gray-900 hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50'
                      }`}
                    >
                      <Icon className="w-6 h-6" />
                      <span className="text-lg">{item.label}</span>
                    </Link>
                  </motion.div>
                )
              })}

              {/* Mobile User Section */}
              <div className="pt-4 border-t border-purple-200/50 mt-4">
                <div className="flex items-center space-x-4 px-6 py-3 text-gray-700">
                  <User className="w-6 h-6" />
                  <span className="text-lg font-semibold">David</span>
                </div>
                <motion.button
                  whileTap={{ scale: 0.95 }}
                  className="flex items-center space-x-4 px-6 py-3 text-red-600 hover:bg-red-50 rounded-xl transition-all duration-300 w-full"
                >
                  <LogOut className="w-6 h-6" />
                  <span className="text-lg font-semibold">Sign Out</span>
                </motion.button>
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </nav>
  )
}

export default AppNavbar
