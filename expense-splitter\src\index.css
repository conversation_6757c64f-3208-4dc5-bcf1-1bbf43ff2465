@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900;
    @apply text-slate-900 dark:text-slate-100;
    @apply font-sans antialiased;
    @apply min-h-screen;
  }

  html {
    @apply scroll-smooth;
  }
}

@layer components {
  /* Glassmorphism effect */
  .glass {
    @apply bg-white/20 dark:bg-white/10;
    @apply backdrop-blur-md;
    @apply border border-white/20 dark:border-white/10;
    @apply shadow-xl;
  }

  /* Enhanced glassmorphism */
  .glass-strong {
    @apply bg-white/30 dark:bg-white/20;
    @apply backdrop-blur-xl;
    @apply border border-white/30 dark:border-white/20;
    @apply shadow-2xl;
  }

  /* Neumorphism effect */
  .neomorphism {
    @apply bg-gradient-to-br from-white to-slate-100 dark:from-slate-800 dark:to-slate-900;
    @apply shadow-[8px_8px_16px_#d1d5db,-8px_-8px_16px_#ffffff] dark:shadow-[8px_8px_16px_#1e293b,-8px_-8px_16px_#334155];
  }

  /* Gradient text */
  .gradient-text {
    @apply bg-gradient-to-r from-primary-600 via-accent-500 to-primary-500;
    @apply bg-clip-text text-transparent;
  }

  /* Animated gradient background */
  .gradient-bg {
    @apply bg-gradient-to-r from-primary-400 via-accent-400 to-primary-600;
    @apply animate-gradient;
    background-size: 200% 200%;
  }

  /* Floating animation */
  .float {
    @apply animate-float;
  }

  /* Glow effect */
  .glow {
    @apply animate-glow;
  }

  /* Custom scrollbar */
  .custom-scrollbar::-webkit-scrollbar {
    @apply w-2;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    @apply bg-slate-100 dark:bg-slate-800 rounded-full;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    @apply bg-primary-400 dark:bg-primary-600 rounded-full;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    @apply bg-primary-500 dark:bg-primary-500;
  }
}

@layer utilities {
  /* Custom utilities */
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }
}


