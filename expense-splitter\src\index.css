@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #f8fafb;
    color: #1a1a1a;
    min-height: 100vh;
    line-height: 1.6;
    font-weight: 400;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  /* Beautiful Card Components */
  .premium-card {
    background: #ffffff;
    border-radius: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.04);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .premium-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  }

  .stats-card {
    background: linear-gradient(135deg, #6ee7b7 0%, #10b981 100%);
    border-radius: 24px;
    color: white;
    box-shadow: 0 8px 32px rgba(16, 185, 129, 0.3);
    border: none;
  }

  .expense-card {
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
    border: 1px solid #f1f5f9;
    transition: all 0.2s ease;
  }

  .expense-card:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border-color: #e2e8f0;
  }

  /* Beautiful Buttons */
  .btn-primary {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    border: none;
    border-radius: 16px;
    font-weight: 600;
    font-size: 16px;
    padding: 14px 28px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 16px rgba(16, 185, 129, 0.3);
  }

  .btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 24px rgba(16, 185, 129, 0.4);
  }

  .btn-secondary {
    background: #f8fafc;
    color: #64748b;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    font-weight: 500;
    padding: 10px 20px;
    transition: all 0.2s ease;
  }

  .btn-secondary:hover {
    background: #f1f5f9;
    border-color: #cbd5e1;
  }

  /* Input Styles */
  .premium-input {
    background: #ffffff;
    border: 2px solid #f1f5f9;
    border-radius: 12px;
    padding: 14px 16px;
    font-size: 16px;
    transition: all 0.2s ease;
    width: 100%;
  }

  .premium-input:focus {
    outline: none;
    border-color: #10b981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
  }
}

@layer utilities {
  /* Beautiful Animations */
  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  .animate-slide-up {
    animation: slideUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .animate-fade-in {
    animation: fadeIn 0.4s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Typography */
  .text-gradient {
    background: linear-gradient(135deg, #10b981, #059669);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .text-soft {
    color: #64748b;
  }

  .text-dark {
    color: #1e293b;
  }

  /* Spacing */
  .container-mobile {
    max-width: 428px;
    margin: 0 auto;
    padding: 0 20px;
  }

  /* Shadows */
  .shadow-soft {
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  }

  .shadow-medium {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  }

  .shadow-strong {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  }
}


