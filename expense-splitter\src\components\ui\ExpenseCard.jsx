import { motion } from 'framer-motion'
import { 
  Calendar, 
  Users, 
  DollarSign, 
  MoreVertical,
  Edit,
  Trash2,
  Eye
} from 'lucide-react'
import { useState } from 'react'

function ExpenseCard({ expense, onEdit, onDelete, onView }) {
  const [showMenu, setShowMenu] = useState(false)

  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const getCategoryColor = (category) => {
    const colors = {
      food: 'from-orange-400 to-red-500',
      transport: 'from-blue-400 to-indigo-500',
      entertainment: 'from-purple-400 to-pink-500',
      shopping: 'from-green-400 to-emerald-500',
      utilities: 'from-yellow-400 to-orange-500',
      other: 'from-gray-400 to-slate-500'
    }
    return colors[category?.toLowerCase()] || colors.other
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      exit={{ opacity: 0, y: -20, scale: 0.95 }}
      whileHover={{ y: -5, scale: 1.02 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
      className="glass rounded-2xl p-6 hover:glass-strong transition-all duration-300 group relative overflow-hidden"
    >
      {/* Background Gradient */}
      <div className={`absolute inset-0 bg-gradient-to-br ${getCategoryColor(expense.category)} opacity-5 group-hover:opacity-10 transition-opacity duration-300`} />
      
      {/* Header */}
      <div className="flex items-start justify-between mb-4 relative z-10">
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-slate-800 dark:text-slate-200 mb-1 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-300">
            {expense.title}
          </h3>
          <p className="text-sm text-slate-500 dark:text-slate-400">
            {expense.description}
          </p>
        </div>
        
        {/* Menu Button */}
        <div className="relative">
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={() => setShowMenu(!showMenu)}
            className="p-2 rounded-lg hover:bg-white/20 transition-colors duration-200"
          >
            <MoreVertical size={16} className="text-slate-400" />
          </motion.button>
          
          {/* Dropdown Menu */}
          {showMenu && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95, y: -10 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: -10 }}
              className="absolute right-0 top-full mt-2 w-40 glass-strong rounded-xl shadow-xl z-20"
            >
              <div className="py-2">
                <button
                  onClick={() => {
                    onView?.(expense)
                    setShowMenu(false)
                  }}
                  className="w-full px-4 py-2 text-left text-sm hover:bg-white/20 transition-colors duration-200 flex items-center space-x-2"
                >
                  <Eye size={14} />
                  <span>View Details</span>
                </button>
                <button
                  onClick={() => {
                    onEdit?.(expense)
                    setShowMenu(false)
                  }}
                  className="w-full px-4 py-2 text-left text-sm hover:bg-white/20 transition-colors duration-200 flex items-center space-x-2"
                >
                  <Edit size={14} />
                  <span>Edit</span>
                </button>
                <button
                  onClick={() => {
                    onDelete?.(expense)
                    setShowMenu(false)
                  }}
                  className="w-full px-4 py-2 text-left text-sm hover:bg-red-500/20 text-red-500 transition-colors duration-200 flex items-center space-x-2"
                >
                  <Trash2 size={14} />
                  <span>Delete</span>
                </button>
              </div>
            </motion.div>
          )}
        </div>
      </div>

      {/* Amount */}
      <div className="mb-4 relative z-10">
        <div className="flex items-center space-x-2">
          <div className={`p-2 rounded-lg bg-gradient-to-r ${getCategoryColor(expense.category)} text-white`}>
            <DollarSign size={16} />
          </div>
          <div>
            <div className="text-2xl font-bold text-slate-800 dark:text-slate-200">
              {formatCurrency(expense.amount)}
            </div>
            <div className="text-sm text-slate-500 dark:text-slate-400">
              Total Amount
            </div>
          </div>
        </div>
      </div>

      {/* Details */}
      <div className="grid grid-cols-2 gap-4 mb-4 relative z-10">
        <div className="flex items-center space-x-2 text-sm text-slate-600 dark:text-slate-400">
          <Calendar size={14} />
          <span>{formatDate(expense.date)}</span>
        </div>
        <div className="flex items-center space-x-2 text-sm text-slate-600 dark:text-slate-400">
          <Users size={14} />
          <span>{expense.participants?.length || 0} people</span>
        </div>
      </div>

      {/* Category Badge */}
      <div className="flex items-center justify-between relative z-10">
        <div className={`px-3 py-1 rounded-full bg-gradient-to-r ${getCategoryColor(expense.category)} text-white text-xs font-medium`}>
          {expense.category || 'Other'}
        </div>
        
        {/* Your Share */}
        <div className="text-right">
          <div className="text-sm text-slate-500 dark:text-slate-400">Your share</div>
          <div className="font-semibold text-primary-600 dark:text-primary-400">
            {formatCurrency(expense.yourShare || expense.amount / (expense.participants?.length || 1))}
          </div>
        </div>
      </div>

      {/* Participants Preview */}
      {expense.participants && expense.participants.length > 0 && (
        <div className="mt-4 pt-4 border-t border-white/20 relative z-10">
          <div className="flex items-center space-x-2">
            <span className="text-xs text-slate-500 dark:text-slate-400">Split between:</span>
            <div className="flex -space-x-2">
              {expense.participants.slice(0, 3).map((participant, index) => (
                <div
                  key={index}
                  className="w-6 h-6 rounded-full bg-gradient-to-r from-primary-400 to-accent-400 flex items-center justify-center text-xs text-white font-medium border-2 border-white dark:border-slate-800"
                  title={participant.name}
                >
                  {participant.name?.charAt(0).toUpperCase()}
                </div>
              ))}
              {expense.participants.length > 3 && (
                <div className="w-6 h-6 rounded-full bg-slate-400 flex items-center justify-center text-xs text-white font-medium border-2 border-white dark:border-slate-800">
                  +{expense.participants.length - 3}
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Hover Effect Overlay */}
      <div className="absolute inset-0 bg-gradient-to-r from-primary-500/5 to-accent-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl" />
    </motion.div>
  )
}

export default ExpenseCard
