import { motion } from 'framer-motion'
import { <PERSON> } from 'react-router-dom'
import {
  <PERSON>R<PERSON>,
  <PERSON>,
  <PERSON>u,
  <PERSON>,
  <PERSON>,
  Users,
  TrendingUp,
  Shield,
  Star,
  Github,
  Twitter,
  Linkedin
} from 'lucide-react'
import { useState } from 'react'

// Floating blob component
const FloatingBlob = ({ className, delay = 0 }) => (
  <motion.div
    className={`absolute rounded-full mix-blend-multiply filter blur-xl opacity-70 ${className}`}
    animate={{
      x: [0, 100, 0],
      y: [0, -100, 0],
      scale: [1, 1.1, 1],
    }}
    transition={{
      duration: 20,
      repeat: Infinity,
      delay,
    }}
  />
)

function Home() {
  const [isDarkMode, setIsDarkMode] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  const toggleDarkMode = () => {
    setIsDarkMode(!isDarkMode)
    document.documentElement.classList.toggle('dark')
  }

  return (
    <div className={`min-h-screen transition-colors duration-300 ${
      isDarkMode
        ? 'bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 text-white'
        : 'bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 text-gray-900'
    }`}>
      {/* Animated Background Blobs */}
      <div className="absolute inset-0 overflow-hidden">
        <FloatingBlob
          className={`w-72 h-72 ${isDarkMode ? 'bg-purple-500/20' : 'bg-purple-300/30'} top-0 left-1/4`}
          delay={0}
        />
        <FloatingBlob
          className={`w-96 h-96 ${isDarkMode ? 'bg-blue-500/20' : 'bg-blue-300/30'} bottom-0 right-1/4`}
          delay={5}
        />
        <FloatingBlob
          className={`w-80 h-80 ${isDarkMode ? 'bg-teal-500/20' : 'bg-teal-300/30'} top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2`}
          delay={10}
        />
      </div>

      {/* Navigation */}
      <nav className={`relative z-50 backdrop-blur-md ${
        isDarkMode ? 'bg-white/5' : 'bg-white/70'
      } border-b ${isDarkMode ? 'border-white/10' : 'border-white/20'}`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            {/* Brand */}
            <div className="flex items-center">
              <div className={`w-8 h-8 rounded-lg bg-gradient-to-r from-teal-500 to-blue-500 mr-3`}></div>
              <span className="text-xl font-bold bg-gradient-to-r from-teal-600 to-blue-600 bg-clip-text text-transparent">
                SplitWise
              </span>
            </div>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-8">
              <a href="#features" className={`${isDarkMode ? 'text-white/80 hover:text-white' : 'text-gray-600 hover:text-gray-900'} transition-colors`}>
                Features
              </a>
              <a href="#pricing" className={`${isDarkMode ? 'text-white/80 hover:text-white' : 'text-gray-600 hover:text-gray-900'} transition-colors`}>
                Pricing
              </a>
              <a href="#about" className={`${isDarkMode ? 'text-white/80 hover:text-white' : 'text-gray-600 hover:text-gray-900'} transition-colors`}>
                About
              </a>
            </div>

            {/* Right side controls */}
            <div className="flex items-center space-x-4">
              {/* Dark mode toggle */}
              <button
                onClick={toggleDarkMode}
                className={`p-2 rounded-lg ${
                  isDarkMode ? 'bg-white/10 hover:bg-white/20' : 'bg-gray-100 hover:bg-gray-200'
                } transition-colors`}
              >
                {isDarkMode ? <Sun className="w-5 h-5" /> : <Moon className="w-5 h-5" />}
              </button>

              {/* Mobile menu button */}
              <button
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                className={`md:hidden p-2 rounded-lg ${
                  isDarkMode ? 'bg-white/10 hover:bg-white/20' : 'bg-gray-100 hover:bg-gray-200'
                } transition-colors`}
              >
                <Menu className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* Mobile menu */}
          {isMobileMenuOpen && (
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              className={`md:hidden py-4 border-t ${isDarkMode ? 'border-white/10' : 'border-gray-200'}`}
            >
              <div className="flex flex-col space-y-4">
                <a href="#features" className={`${isDarkMode ? 'text-white/80' : 'text-gray-600'}`}>Features</a>
                <a href="#pricing" className={`${isDarkMode ? 'text-white/80' : 'text-gray-600'}`}>Pricing</a>
                <a href="#about" className={`${isDarkMode ? 'text-white/80' : 'text-gray-600'}`}>About</a>
              </div>
            </motion.div>
          )}
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative z-10 max-w-7xl mx-auto px-4 py-20">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left side - Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            className="space-y-8"
          >
            {/* Badge */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className={`inline-flex items-center px-4 py-2 rounded-full border ${
                isDarkMode
                  ? 'bg-white/10 backdrop-blur-sm border-white/20 text-white'
                  : 'bg-white/60 backdrop-blur-sm border-white/40 text-gray-700'
              }`}
            >
              <Star className="w-4 h-4 mr-2 text-yellow-500" />
              <span className="text-sm font-medium">Trusted by 50,000+ users</span>
            </motion.div>

            {/* Main Headline */}
            <motion.h1
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              className="text-5xl lg:text-6xl font-bold leading-tight"
            >
              Split Expenses{' '}
              <span className="bg-gradient-to-r from-teal-500 via-blue-500 to-purple-500 bg-clip-text text-transparent">
                Effortlessly
              </span>
            </motion.h1>

            {/* Description */}
            <motion.p
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className={`text-xl leading-relaxed ${
                isDarkMode ? 'text-white/80' : 'text-gray-600'
              }`}
            >
              The most intuitive way to split bills with friends, track group expenses,
              and settle up instantly. Beautiful design meets powerful functionality.
            </motion.p>

            {/* CTA Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.5 }}
              className="flex flex-col sm:flex-row gap-4"
            >
              <Link to="/dashboard">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="px-8 py-4 bg-gradient-to-r from-teal-500 to-blue-500 text-white rounded-2xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center space-x-2"
                >
                  <span>Get Started Free</span>
                  <ArrowRight className="w-5 h-5" />
                </motion.button>
              </Link>

              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className={`px-8 py-4 rounded-2xl font-semibold border-2 transition-all duration-300 flex items-center justify-center space-x-2 ${
                  isDarkMode
                    ? 'border-white/20 text-white hover:bg-white/10'
                    : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
              >
                <Play className="w-5 h-5" />
                <span>Watch Demo</span>
              </motion.button>
            </motion.div>
          </motion.div>

          {/* Right side - 3D Visual */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="relative hidden lg:block"
          >
            {/* 3D Card Stack */}
            <div className="relative w-full h-96">
              {/* Card 1 */}
              <motion.div
                animate={{
                  rotateY: [0, 5, 0],
                  rotateX: [0, 2, 0],
                }}
                transition={{ duration: 6, repeat: Infinity }}
                className={`absolute top-0 left-8 w-80 h-48 rounded-3xl shadow-2xl ${
                  isDarkMode ? 'bg-gradient-to-br from-purple-600 to-blue-600' : 'bg-gradient-to-br from-purple-400 to-blue-400'
                } p-6 text-white transform rotate-3`}
              >
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="font-semibold text-lg">Dinner Split</h3>
                    <p className="text-white/80 text-sm">4 people</p>
                  </div>
                  <div className="text-right">
                    <p className="text-2xl font-bold">$240</p>
                    <p className="text-white/80 text-sm">Total</p>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm">You owe</span>
                    <span className="font-semibold">$60</span>
                  </div>
                  <div className="w-full bg-white/20 rounded-full h-2">
                    <div className="bg-white rounded-full h-2 w-1/4"></div>
                  </div>
                </div>
              </motion.div>

              {/* Card 2 */}
              <motion.div
                animate={{
                  rotateY: [0, -3, 0],
                  rotateX: [0, -1, 0],
                }}
                transition={{ duration: 8, repeat: Infinity, delay: 2 }}
                className={`absolute top-12 right-8 w-80 h-48 rounded-3xl shadow-2xl ${
                  isDarkMode ? 'bg-gradient-to-br from-teal-600 to-green-600' : 'bg-gradient-to-br from-teal-400 to-green-400'
                } p-6 text-white transform -rotate-2`}
              >
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="font-semibold text-lg">Trip Expenses</h3>
                    <p className="text-white/80 text-sm">6 people</p>
                  </div>
                  <div className="text-right">
                    <p className="text-2xl font-bold">$1,200</p>
                    <p className="text-white/80 text-sm">Total</p>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm">You're owed</span>
                    <span className="font-semibold text-green-200">+$45</span>
                  </div>
                  <div className="w-full bg-white/20 rounded-full h-2">
                    <div className="bg-green-200 rounded-full h-2 w-3/4"></div>
                  </div>
                </div>
              </motion.div>

              {/* Floating elements */}
              <motion.div
                animate={{ y: [0, -20, 0] }}
                transition={{ duration: 4, repeat: Infinity }}
                className={`absolute top-4 right-4 w-16 h-16 rounded-2xl ${
                  isDarkMode ? 'bg-white/10' : 'bg-white/60'
                } backdrop-blur-sm flex items-center justify-center`}
              >
                <Users className={`w-8 h-8 ${isDarkMode ? 'text-white' : 'text-gray-700'}`} />
              </motion.div>

              <motion.div
                animate={{ y: [0, 15, 0] }}
                transition={{ duration: 5, repeat: Infinity, delay: 1 }}
                className={`absolute bottom-4 left-4 w-16 h-16 rounded-2xl ${
                  isDarkMode ? 'bg-white/10' : 'bg-white/60'
                } backdrop-blur-sm flex items-center justify-center`}
              >
                <TrendingUp className={`w-8 h-8 ${isDarkMode ? 'text-white' : 'text-gray-700'}`} />
              </motion.div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="relative z-10 max-w-7xl mx-auto px-4 py-20">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className={`rounded-3xl p-8 md:p-12 ${
            isDarkMode
              ? 'bg-white/5 backdrop-blur-md border border-white/10'
              : 'bg-white/60 backdrop-blur-md border border-white/20'
          }`}
        >
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {[
              { number: '50K+', label: 'Happy Users', icon: Users },
              { number: '$5M+', label: 'Expenses Tracked', icon: TrendingUp },
              { number: '99.9%', label: 'Uptime', icon: Shield },
              { number: '4.9★', label: 'App Store Rating', icon: Star }
            ].map((stat, index) => {
              const Icon = stat.icon
              return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 + index * 0.1 }}
                  className="text-center"
                >
                  <div className={`inline-flex items-center justify-center w-12 h-12 rounded-2xl mb-4 ${
                    isDarkMode ? 'bg-white/10' : 'bg-white/40'
                  }`}>
                    <Icon className={`w-6 h-6 ${
                      isDarkMode ? 'text-white' : 'text-gray-700'
                    }`} />
                  </div>
                  <div className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-teal-500 to-blue-500 bg-clip-text text-transparent mb-2">
                    {stat.number}
                  </div>
                  <div className={`text-sm md:text-base ${
                    isDarkMode ? 'text-white/70' : 'text-gray-600'
                  }`}>
                    {stat.label}
                  </div>
                </motion.div>
              )
            })}
          </div>
        </motion.div>
      </section>

      {/* CTA Section */}
      <section className="relative z-10 max-w-7xl mx-auto px-4 py-20">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className={`text-center rounded-3xl p-12 md:p-16 ${
            isDarkMode
              ? 'bg-gradient-to-r from-purple-600/20 to-blue-600/20 backdrop-blur-md border border-white/10'
              : 'bg-gradient-to-r from-purple-100/60 to-blue-100/60 backdrop-blur-md border border-white/20'
          }`}
        >
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-4xl md:text-5xl font-bold mb-6"
          >
            Ready to Split Your{' '}
            <span className="bg-gradient-to-r from-teal-500 to-blue-500 bg-clip-text text-transparent">
              Expenses?
            </span>
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            className={`text-xl mb-8 max-w-2xl mx-auto ${
              isDarkMode ? 'text-white/80' : 'text-gray-600'
            }`}
          >
            Join thousands of users who have simplified their expense splitting.
            Start for free and experience the difference.
          </motion.p>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="flex flex-col sm:flex-row gap-4 justify-center"
          >
            <Link to="/dashboard">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-12 py-4 bg-gradient-to-r from-teal-500 to-blue-500 text-white rounded-2xl font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300"
              >
                Start Free Today
              </motion.button>
            </Link>

            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className={`px-12 py-4 rounded-2xl font-semibold border-2 transition-all duration-300 ${
                isDarkMode
                  ? 'border-white/20 text-white hover:bg-white/10'
                  : 'border-gray-300 text-gray-700 hover:bg-gray-50'
              }`}
            >
              Learn More
            </motion.button>
          </motion.div>
        </motion.div>
      </section>

      {/* Footer */}
      <footer className={`relative z-10 border-t ${
        isDarkMode ? 'border-white/10 bg-black/20' : 'border-gray-200 bg-white/30'
      } backdrop-blur-md`}>
        <div className="max-w-7xl mx-auto px-4 py-12">
          <div className="grid md:grid-cols-4 gap-8">
            {/* Brand */}
            <div className="md:col-span-2">
              <div className="flex items-center mb-4">
                <div className="w-8 h-8 rounded-lg bg-gradient-to-r from-teal-500 to-blue-500 mr-3"></div>
                <span className="text-xl font-bold bg-gradient-to-r from-teal-600 to-blue-600 bg-clip-text text-transparent">
                  SplitWise
                </span>
              </div>
              <p className={`${isDarkMode ? 'text-white/70' : 'text-gray-600'} max-w-md`}>
                The most beautiful and intuitive expense splitting app.
                Split bills, track expenses, and settle up with friends effortlessly.
              </p>
            </div>

            {/* Links */}
            <div>
              <h3 className={`font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                Product
              </h3>
              <div className="space-y-2">
                <a href="#" className={`block ${isDarkMode ? 'text-white/70 hover:text-white' : 'text-gray-600 hover:text-gray-900'} transition-colors`}>
                  Features
                </a>
                <a href="#" className={`block ${isDarkMode ? 'text-white/70 hover:text-white' : 'text-gray-600 hover:text-gray-900'} transition-colors`}>
                  Pricing
                </a>
                <a href="#" className={`block ${isDarkMode ? 'text-white/70 hover:text-white' : 'text-gray-600 hover:text-gray-900'} transition-colors`}>
                  Security
                </a>
              </div>
            </div>

            {/* Social */}
            <div>
              <h3 className={`font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                Connect
              </h3>
              <div className="flex space-x-4">
                <a href="#" className={`${isDarkMode ? 'text-white/70 hover:text-white' : 'text-gray-600 hover:text-gray-900'} transition-colors`}>
                  <Twitter className="w-5 h-5" />
                </a>
                <a href="#" className={`${isDarkMode ? 'text-white/70 hover:text-white' : 'text-gray-600 hover:text-gray-900'} transition-colors`}>
                  <Github className="w-5 h-5" />
                </a>
                <a href="#" className={`${isDarkMode ? 'text-white/70 hover:text-white' : 'text-gray-600 hover:text-gray-900'} transition-colors`}>
                  <Linkedin className="w-5 h-5" />
                </a>
              </div>
            </div>
          </div>

          <div className={`border-t ${isDarkMode ? 'border-white/10' : 'border-gray-200'} mt-8 pt-8 text-center`}>
            <p className={`${isDarkMode ? 'text-white/60' : 'text-gray-500'}`}>
              © 2024 SplitWise. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}

export default Home
