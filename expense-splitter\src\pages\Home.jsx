import { motion } from 'framer-motion'
import { <PERSON> } from 'react-router-dom'
import {
  <PERSON><PERSON><PERSON>,
  Play,
  <PERSON>u,
  <PERSON>,
  Sun,
  Users,
  TrendingUp,
  Shield,
  Star,
  Github,
  Twitter,
  Linkedin,
  DollarSign,
  PieChart,
  Smartphone,
  Zap,
  Heart,
  CheckCircle,
  Sparkles,
  Globe
} from 'lucide-react'
import { useState, useEffect } from 'react'

// Enhanced floating blob component with more colors
const FloatingBlob = ({ className, delay = 0 }) => (
  <motion.div
    className={`absolute rounded-full mix-blend-multiply filter blur-xl opacity-60 ${className}`}
    animate={{
      x: [0, 100, -50, 0],
      y: [0, -100, 50, 0],
      scale: [1, 1.2, 0.8, 1],
      rotate: [0, 180, 360],
    }}
    transition={{
      duration: 25,
      repeat: Infinity,
      delay,
      ease: "easeInOut",
    }}
  />
)

// Animated gradient text component
const GradientText = ({ children, className = "" }) => (
  <span className={`bg-gradient-to-r from-pink-500 via-purple-500 to-indigo-500 bg-clip-text text-transparent animate-pulse ${className}`}>
    {children}
  </span>
)

// Feature card component
const FeatureCard = ({ icon: Icon, title, description, color, delay = 0 }) => (
  <motion.div
    initial={{ opacity: 0, y: 50 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.6, delay }}
    className={`relative p-8 rounded-3xl bg-white/80 backdrop-blur-lg border border-white/20 shadow-2xl hover:shadow-3xl transition-all duration-300 hover:-translate-y-2 group overflow-hidden`}
  >
    <div className={`absolute inset-0 bg-gradient-to-br ${color} opacity-0 group-hover:opacity-10 transition-opacity duration-300`} />
    <div className={`w-16 h-16 rounded-2xl bg-gradient-to-br ${color} flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
      <Icon className="w-8 h-8 text-white" />
    </div>
    <h3 className="text-xl font-bold text-gray-900 mb-3">{title}</h3>
    <p className="text-gray-600 leading-relaxed">{description}</p>
  </motion.div>
)

function Home() {
  const [isDarkMode, setIsDarkMode] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })

  const toggleDarkMode = () => {
    setIsDarkMode(!isDarkMode)
    document.documentElement.classList.toggle('dark')
  }

  // Mouse tracking for interactive effects
  useEffect(() => {
    const handleMouseMove = (e) => {
      setMousePosition({ x: e.clientX, y: e.clientY })
    }
    window.addEventListener('mousemove', handleMouseMove)
    return () => window.removeEventListener('mousemove', handleMouseMove)
  }, [])

  // Features data
  const features = [
    {
      icon: DollarSign,
      title: "Smart Splitting",
      description: "Automatically calculate and split expenses with intelligent algorithms that ensure fairness.",
      color: "from-emerald-400 to-teal-600",
      delay: 0.1
    },
    {
      icon: PieChart,
      title: "Visual Analytics",
      description: "Beautiful charts and insights to track your spending patterns and group dynamics.",
      color: "from-blue-400 to-indigo-600",
      delay: 0.2
    },
    {
      icon: Smartphone,
      title: "Mobile First",
      description: "Designed for mobile with a responsive interface that works perfectly on any device.",
      color: "from-purple-400 to-pink-600",
      delay: 0.3
    },
    {
      icon: Zap,
      title: "Lightning Fast",
      description: "Instant calculations and real-time updates keep everyone in sync effortlessly.",
      color: "from-yellow-400 to-orange-600",
      delay: 0.4
    }
  ]

  return (
    <div className={`min-h-screen transition-all duration-500 relative overflow-hidden ${
      isDarkMode
        ? 'bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 text-white'
        : 'bg-gradient-to-br from-rose-50 via-blue-50 to-indigo-100 text-gray-900'
    }`}>
      {/* Interactive Mouse Follower */}
      <motion.div
        className="fixed w-6 h-6 bg-gradient-to-r from-pink-400 to-purple-500 rounded-full pointer-events-none z-50 mix-blend-difference"
        animate={{
          x: mousePosition.x - 12,
          y: mousePosition.y - 12,
        }}
        transition={{ type: "spring", stiffness: 500, damping: 28 }}
      />

      {/* Enhanced Animated Background Blobs */}
      <div className="absolute inset-0 overflow-hidden">
        <FloatingBlob
          className={`w-96 h-96 ${isDarkMode ? 'bg-gradient-to-r from-purple-500/30 to-pink-500/30' : 'bg-gradient-to-r from-purple-300/40 to-pink-300/40'} -top-20 -left-20`}
          delay={0}
        />
        <FloatingBlob
          className={`w-80 h-80 ${isDarkMode ? 'bg-gradient-to-r from-blue-500/30 to-teal-500/30' : 'bg-gradient-to-r from-blue-300/40 to-teal-300/40'} top-1/4 right-0`}
          delay={3}
        />
        <FloatingBlob
          className={`w-72 h-72 ${isDarkMode ? 'bg-gradient-to-r from-emerald-500/30 to-blue-500/30' : 'bg-gradient-to-r from-emerald-300/40 to-blue-300/40'} bottom-1/4 left-1/3`}
          delay={6}
        />
        <FloatingBlob
          className={`w-64 h-64 ${isDarkMode ? 'bg-gradient-to-r from-yellow-500/30 to-orange-500/30' : 'bg-gradient-to-r from-yellow-300/40 to-orange-300/40'} bottom-0 right-1/4`}
          delay={9}
        />
        <FloatingBlob
          className={`w-56 h-56 ${isDarkMode ? 'bg-gradient-to-r from-rose-500/30 to-purple-500/30' : 'bg-gradient-to-r from-rose-300/40 to-purple-300/40'} top-1/2 left-0`}
          delay={12}
        />
      </div>

      {/* Sparkle Effects */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(20)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-gradient-to-r from-yellow-400 to-pink-400 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              scale: [0, 1, 0],
              opacity: [0, 1, 0],
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>

      {/* Enhanced Navigation */}
      <nav className={`relative z-50 backdrop-blur-xl ${
        isDarkMode ? 'bg-gradient-to-r from-slate-900/80 to-purple-900/80' : 'bg-gradient-to-r from-white/80 to-blue-50/80'
      } border-b ${isDarkMode ? 'border-purple-500/20' : 'border-blue-200/50'} shadow-lg`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            {/* Enhanced Brand */}
            <motion.div
              className="flex items-center"
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring", stiffness: 400, damping: 10 }}
            >
              <motion.div
                className={`w-10 h-10 rounded-xl bg-gradient-to-br from-pink-500 via-purple-500 to-indigo-500 mr-3 flex items-center justify-center shadow-lg`}
                animate={{ rotate: [0, 360] }}
                transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
              >
                <Sparkles className="w-5 h-5 text-white" />
              </motion.div>
              <span className="text-2xl font-bold bg-gradient-to-r from-pink-500 via-purple-500 to-indigo-500 bg-clip-text text-transparent">
                SplitWise
              </span>
            </motion.div>

            {/* Enhanced Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-6">
              <motion.a
                href="#features"
                className={`px-4 py-2 rounded-full font-medium transition-all duration-300 ${
                  isDarkMode
                    ? 'text-white/80 hover:text-white hover:bg-white/10'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-blue-100/50'
                }`}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Features
              </motion.a>
              <motion.a
                href="#pricing"
                className={`px-4 py-2 rounded-full font-medium transition-all duration-300 ${
                  isDarkMode
                    ? 'text-white/80 hover:text-white hover:bg-white/10'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-purple-100/50'
                }`}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Pricing
              </motion.a>
              <motion.a
                href="#about"
                className={`px-4 py-2 rounded-full font-medium transition-all duration-300 ${
                  isDarkMode
                    ? 'text-white/80 hover:text-white hover:bg-white/10'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-pink-100/50'
                }`}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                About
              </motion.a>
            </div>

            {/* Enhanced Right side controls */}
            <div className="flex items-center space-x-3">
              {/* Get Started Button */}
              <Link to="/dashboard">
                <motion.button
                  className="hidden sm:flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-pink-500 to-purple-600 text-white font-semibold rounded-full shadow-lg hover:shadow-xl transition-all duration-300"
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <span>Get Started</span>
                  <ArrowRight className="w-4 h-4" />
                </motion.button>
              </Link>

              {/* Enhanced Dark mode toggle */}
              <motion.button
                onClick={toggleDarkMode}
                className={`p-3 rounded-full ${
                  isDarkMode
                    ? 'bg-gradient-to-r from-yellow-400 to-orange-500 text-gray-900'
                    : 'bg-gradient-to-r from-indigo-500 to-purple-600 text-white'
                } shadow-lg hover:shadow-xl transition-all duration-300`}
                whileHover={{ scale: 1.1, rotate: 180 }}
                whileTap={{ scale: 0.9 }}
              >
                {isDarkMode ? <Sun className="w-5 h-5" /> : <Moon className="w-5 h-5" />}
              </motion.button>

              {/* Enhanced Mobile menu button */}
              <motion.button
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                className={`md:hidden p-3 rounded-full ${
                  isDarkMode
                    ? 'bg-gradient-to-r from-purple-500 to-pink-500'
                    : 'bg-gradient-to-r from-blue-500 to-teal-500'
                } text-white shadow-lg hover:shadow-xl transition-all duration-300`}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <Menu className="w-5 h-5" />
              </motion.button>
            </div>
          </div>

          {/* Mobile menu */}
          {isMobileMenuOpen && (
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              className={`md:hidden py-4 border-t ${isDarkMode ? 'border-white/10' : 'border-gray-200'}`}
            >
              <div className="flex flex-col space-y-4">
                <a href="#features" className={`${isDarkMode ? 'text-white/80' : 'text-gray-600'}`}>Features</a>
                <a href="#pricing" className={`${isDarkMode ? 'text-white/80' : 'text-gray-600'}`}>Pricing</a>
                <a href="#about" className={`${isDarkMode ? 'text-white/80' : 'text-gray-600'}`}>About</a>
              </div>
            </motion.div>
          )}
        </div>
      </nav>

      {/* Enhanced Hero Section */}
      <section className="relative z-10 max-w-7xl mx-auto px-4 py-24">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Left side - Enhanced Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            className="space-y-10"
          >
            {/* Enhanced Badge with Animation */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className={`inline-flex items-center px-6 py-3 rounded-full ${
                isDarkMode
                  ? 'bg-gradient-to-r from-purple-500/20 to-pink-500/20 backdrop-blur-lg border border-purple-400/30 text-white'
                  : 'bg-gradient-to-r from-blue-100/80 to-purple-100/80 backdrop-blur-lg border border-blue-200/50 text-gray-700'
              } shadow-xl`}
              whileHover={{ scale: 1.05 }}
            >
              <motion.div
                animate={{ rotate: [0, 360] }}
                transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              >
                <Star className="w-5 h-5 mr-3 text-yellow-500" />
              </motion.div>
              <span className="text-sm font-semibold">
                <GradientText>Trusted by 50,000+ users worldwide</GradientText>
              </span>
            </motion.div>

            {/* Enhanced Main Headline */}
            <motion.h1
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              className="text-6xl lg:text-7xl font-black leading-tight"
            >
              <motion.span
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 1, delay: 0.5 }}
              >
                Split Expenses{' '}
              </motion.span>
              <motion.span
                className="bg-gradient-to-r from-pink-500 via-purple-500 to-indigo-500 bg-clip-text text-transparent"
                animate={{
                  backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'],
                }}
                transition={{ duration: 3, repeat: Infinity }}
                style={{ backgroundSize: '200% 200%' }}
              >
                Effortlessly
              </motion.span>
              <motion.span
                initial={{ opacity: 0, scale: 0 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 1 }}
                className="inline-block ml-4"
              >
                ✨
              </motion.span>
            </motion.h1>

            {/* Enhanced Description */}
            <motion.p
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className={`text-xl lg:text-2xl leading-relaxed font-medium ${
                isDarkMode ? 'text-white/90' : 'text-gray-700'
              }`}
            >
              The most <GradientText>intuitive</GradientText> way to split bills with friends,
              track group expenses, and settle up instantly.
              <br />
              <span className="text-lg opacity-80">
                Beautiful design meets powerful functionality 🚀
              </span>
            </motion.p>

            {/* Enhanced CTA Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.5 }}
              className="flex flex-col sm:flex-row gap-6"
            >
              <Link to="/dashboard">
                <motion.button
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  className="group relative px-10 py-5 bg-gradient-to-r from-pink-500 via-purple-500 to-indigo-500 text-white rounded-2xl font-bold text-lg shadow-2xl hover:shadow-purple-500/25 transition-all duration-300 flex items-center justify-center space-x-3 overflow-hidden"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-pink-600 via-purple-600 to-indigo-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  <span className="relative z-10">Get Started Free</span>
                  <motion.div
                    className="relative z-10"
                    animate={{ x: [0, 5, 0] }}
                    transition={{ duration: 1.5, repeat: Infinity }}
                  >
                    <ArrowRight className="w-6 h-6" />
                  </motion.div>
                  <div className="absolute inset-0 bg-white/20 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left" />
                </motion.button>
              </Link>

              <motion.button
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                className={`group px-10 py-5 rounded-2xl font-bold text-lg border-2 transition-all duration-300 flex items-center justify-center space-x-3 backdrop-blur-lg ${
                  isDarkMode
                    ? 'border-purple-400/50 text-white hover:bg-purple-500/20 hover:border-purple-400'
                    : 'border-purple-300/50 text-gray-700 hover:bg-purple-100/50 hover:border-purple-400'
                } shadow-xl hover:shadow-2xl`}
              >
                <motion.div
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{ duration: 2, repeat: Infinity }}
                >
                  <Play className="w-6 h-6 text-purple-500" />
                </motion.div>
                <span>Watch Demo</span>
              </motion.button>
            </motion.div>
          </motion.div>

          {/* Right side - 3D Visual */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="relative hidden lg:block"
          >
            {/* 3D Card Stack */}
            <div className="relative w-full h-96">
              {/* Card 1 */}
              <motion.div
                animate={{
                  rotateY: [0, 5, 0],
                  rotateX: [0, 2, 0],
                }}
                transition={{ duration: 6, repeat: Infinity }}
                className={`absolute top-0 left-8 w-80 h-48 rounded-3xl shadow-2xl ${
                  isDarkMode ? 'bg-gradient-to-br from-purple-600 to-blue-600' : 'bg-gradient-to-br from-purple-400 to-blue-400'
                } p-6 text-white transform rotate-3`}
              >
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="font-semibold text-lg">Dinner Split</h3>
                    <p className="text-white/80 text-sm">4 people</p>
                  </div>
                  <div className="text-right">
                    <p className="text-2xl font-bold">$240</p>
                    <p className="text-white/80 text-sm">Total</p>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm">You owe</span>
                    <span className="font-semibold">$60</span>
                  </div>
                  <div className="w-full bg-white/20 rounded-full h-2">
                    <div className="bg-white rounded-full h-2 w-1/4"></div>
                  </div>
                </div>
              </motion.div>

              {/* Card 2 */}
              <motion.div
                animate={{
                  rotateY: [0, -3, 0],
                  rotateX: [0, -1, 0],
                }}
                transition={{ duration: 8, repeat: Infinity, delay: 2 }}
                className={`absolute top-12 right-8 w-80 h-48 rounded-3xl shadow-2xl ${
                  isDarkMode ? 'bg-gradient-to-br from-teal-600 to-green-600' : 'bg-gradient-to-br from-teal-400 to-green-400'
                } p-6 text-white transform -rotate-2`}
              >
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="font-semibold text-lg">Trip Expenses</h3>
                    <p className="text-white/80 text-sm">6 people</p>
                  </div>
                  <div className="text-right">
                    <p className="text-2xl font-bold">$1,200</p>
                    <p className="text-white/80 text-sm">Total</p>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm">You're owed</span>
                    <span className="font-semibold text-green-200">+$45</span>
                  </div>
                  <div className="w-full bg-white/20 rounded-full h-2">
                    <div className="bg-green-200 rounded-full h-2 w-3/4"></div>
                  </div>
                </div>
              </motion.div>

              {/* Floating elements */}
              <motion.div
                animate={{ y: [0, -20, 0] }}
                transition={{ duration: 4, repeat: Infinity }}
                className={`absolute top-4 right-4 w-16 h-16 rounded-2xl ${
                  isDarkMode ? 'bg-white/10' : 'bg-white/60'
                } backdrop-blur-sm flex items-center justify-center`}
              >
                <Users className={`w-8 h-8 ${isDarkMode ? 'text-white' : 'text-gray-700'}`} />
              </motion.div>

              <motion.div
                animate={{ y: [0, 15, 0] }}
                transition={{ duration: 5, repeat: Infinity, delay: 1 }}
                className={`absolute bottom-4 left-4 w-16 h-16 rounded-2xl ${
                  isDarkMode ? 'bg-white/10' : 'bg-white/60'
                } backdrop-blur-sm flex items-center justify-center`}
              >
                <TrendingUp className={`w-8 h-8 ${isDarkMode ? 'text-white' : 'text-gray-700'}`} />
              </motion.div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Enhanced Features Section */}
      <section id="features" className="relative z-10 max-w-7xl mx-auto px-4 py-24">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-20"
        >
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-5xl lg:text-6xl font-bold mb-6"
          >
            Why Choose{' '}
            <GradientText>SplitWise</GradientText>?
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            className={`text-xl lg:text-2xl ${isDarkMode ? 'text-white/80' : 'text-gray-600'} max-w-3xl mx-auto`}
          >
            Experience the perfect blend of simplicity and power with features designed for modern expense sharing.
          </motion.p>
        </motion.div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <FeatureCard
              key={index}
              icon={feature.icon}
              title={feature.title}
              description={feature.description}
              color={feature.color}
              delay={feature.delay}
            />
          ))}
        </div>

        {/* Additional Features Row */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="grid md:grid-cols-3 gap-8 mt-16"
        >
          <FeatureCard
            icon={Heart}
            title="User Friendly"
            description="Intuitive interface that anyone can use without training or tutorials."
            color="from-red-400 to-pink-600"
            delay={0.7}
          />
          <FeatureCard
            icon={Globe}
            title="Multi-Currency"
            description="Support for 150+ currencies with real-time exchange rates."
            color="from-green-400 to-emerald-600"
            delay={0.8}
          />
          <FeatureCard
            icon={CheckCircle}
            title="Secure & Private"
            description="Bank-level security with end-to-end encryption for all your data."
            color="from-indigo-400 to-blue-600"
            delay={0.9}
          />
        </motion.div>
      </section>

      {/* Enhanced Colorful Stats Section */}
      <section className="relative z-10 max-w-7xl mx-auto px-4 py-24">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl lg:text-5xl font-bold mb-4">
            Trusted by <GradientText>Thousands</GradientText> Worldwide
          </h2>
          <p className={`text-lg ${isDarkMode ? 'text-white/70' : 'text-gray-600'}`}>
            Join the community that's revolutionizing expense sharing
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className={`rounded-3xl p-8 md:p-16 ${
            isDarkMode
              ? 'bg-gradient-to-br from-purple-900/30 to-blue-900/30 backdrop-blur-xl border border-purple-500/20'
              : 'bg-gradient-to-br from-blue-50/80 to-purple-50/80 backdrop-blur-xl border border-blue-200/30'
          } shadow-2xl`}
        >
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {[
              { number: '50K+', label: 'Happy Users', icon: Users, color: 'from-blue-400 to-blue-600' },
              { number: '$5M+', label: 'Expenses Tracked', icon: TrendingUp, color: 'from-green-400 to-emerald-600' },
              { number: '99.9%', label: 'Uptime', icon: Shield, color: 'from-purple-400 to-purple-600' },
              { number: '4.9★', label: 'App Store Rating', icon: Star, color: 'from-yellow-400 to-orange-500' }
            ].map((stat, index) => {
              const Icon = stat.icon
              return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.3 + index * 0.1 }}
                  className="text-center group"
                  whileHover={{ scale: 1.05 }}
                >
                  <motion.div
                    className={`inline-flex items-center justify-center w-16 h-16 rounded-2xl mb-6 bg-gradient-to-br ${stat.color} shadow-lg group-hover:shadow-xl transition-all duration-300`}
                    whileHover={{ rotate: 360 }}
                    transition={{ duration: 0.6 }}
                  >
                    <Icon className="w-8 h-8 text-white" />
                  </motion.div>
                  <motion.div
                    className="text-4xl md:text-5xl font-black mb-3"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ duration: 0.5, delay: 0.5 + index * 0.1 }}
                  >
                    <GradientText>{stat.number}</GradientText>
                  </motion.div>
                  <div className={`text-sm md:text-base font-medium ${
                    isDarkMode ? 'text-white/80' : 'text-gray-700'
                  }`}>
                    {stat.label}
                  </div>
                </motion.div>
              )
            })}
          </div>
        </motion.div>
      </section>

      {/* Enhanced Final CTA Section */}
      <section className="relative z-10 max-w-7xl mx-auto px-4 py-24">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className={`text-center rounded-3xl p-12 md:p-20 relative overflow-hidden ${
            isDarkMode
              ? 'bg-gradient-to-br from-purple-600/30 via-pink-600/20 to-indigo-600/30 backdrop-blur-xl border border-purple-400/30'
              : 'bg-gradient-to-br from-purple-100/80 via-pink-100/60 to-indigo-100/80 backdrop-blur-xl border border-purple-200/50'
          } shadow-2xl`}
        >
          {/* Background decoration */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-pink-400/20 to-purple-600/20 rounded-full blur-3xl" />
            <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-indigo-600/20 rounded-full blur-3xl" />
          </div>

          <div className="relative z-10">
            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="text-5xl md:text-6xl font-black mb-8"
            >
              Ready to Split Your{' '}
              <motion.span
                className="bg-gradient-to-r from-pink-500 via-purple-500 to-indigo-500 bg-clip-text text-transparent"
                animate={{
                  backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'],
                }}
                transition={{ duration: 4, repeat: Infinity }}
                style={{ backgroundSize: '200% 200%' }}
              >
                Expenses?
              </motion.span>
              <motion.span
                initial={{ opacity: 0, scale: 0 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 1 }}
                className="inline-block ml-4"
              >
                🚀
              </motion.span>
            </motion.h2>

            <motion.p
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              className={`text-xl md:text-2xl mb-12 max-w-3xl mx-auto font-medium ${
                isDarkMode ? 'text-white/90' : 'text-gray-700'
              }`}
            >
              Join <GradientText>thousands of users</GradientText> who have simplified their expense splitting.
              Start your journey to stress-free group expenses today! ✨
            Start for free and experience the difference.
          </motion.p>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="flex flex-col sm:flex-row gap-6 justify-center items-center"
            >
              <Link to="/dashboard">
                <motion.button
                  whileHover={{ scale: 1.05, y: -3 }}
                  whileTap={{ scale: 0.95 }}
                  className="group relative px-12 py-5 bg-gradient-to-r from-pink-500 via-purple-500 to-indigo-500 text-white rounded-2xl font-bold text-lg shadow-2xl hover:shadow-purple-500/25 transition-all duration-300 overflow-hidden"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-pink-600 via-purple-600 to-indigo-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  <span className="relative z-10 flex items-center space-x-3">
                    <span>Start Free Today</span>
                    <motion.div
                      animate={{ x: [0, 5, 0] }}
                      transition={{ duration: 1.5, repeat: Infinity }}
                    >
                      <ArrowRight className="w-5 h-5" />
                    </motion.div>
                  </span>
                </motion.button>
              </Link>

              <motion.button
                whileHover={{ scale: 1.05, y: -3 }}
                whileTap={{ scale: 0.95 }}
                className={`px-12 py-5 rounded-2xl font-bold text-lg border-2 transition-all duration-300 backdrop-blur-lg ${
                  isDarkMode
                    ? 'border-purple-400/50 text-white hover:bg-purple-500/20 hover:border-purple-400'
                    : 'border-purple-300/50 text-gray-700 hover:bg-purple-100/50 hover:border-purple-400'
                } shadow-xl hover:shadow-2xl`}
            >
              <span className="flex items-center space-x-2">
                <span>Learn More</span>
                <Play className="w-5 h-5" />
              </span>
            </motion.button>
            </motion.div>
          </div>
        </motion.div>
      </section>

      {/* Enhanced Beautiful Footer */}
      <footer className={`relative z-10 border-t ${
        isDarkMode
          ? 'border-purple-500/20 bg-gradient-to-br from-slate-900/80 to-purple-900/80'
          : 'border-purple-200/50 bg-gradient-to-br from-white/80 to-purple-50/80'
      } backdrop-blur-xl`}>
        <div className="max-w-7xl mx-auto px-4 py-16">
          <div className="grid md:grid-cols-4 gap-12">
            {/* Enhanced Brand */}
            <div className="md:col-span-2">
              <motion.div
                className="flex items-center mb-6"
                whileHover={{ scale: 1.05 }}
              >
                <motion.div
                  className="w-12 h-12 rounded-xl bg-gradient-to-br from-pink-500 via-purple-500 to-indigo-500 mr-4 flex items-center justify-center shadow-lg"
                  animate={{ rotate: [0, 360] }}
                  transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                >
                  <Sparkles className="w-6 h-6 text-white" />
                </motion.div>
                <span className="text-2xl font-bold bg-gradient-to-r from-pink-500 via-purple-500 to-indigo-500 bg-clip-text text-transparent">
                  SplitWise
                </span>
              </motion.div>
              <p className={`${isDarkMode ? 'text-white/80' : 'text-gray-700'} max-w-md text-lg leading-relaxed mb-6`}>
                The most <GradientText>beautiful and intuitive</GradientText> expense splitting app.
                Split bills, track expenses, and settle up with friends effortlessly. ✨
              </p>
              <div className="flex space-x-4">
                {[
                  { icon: Twitter, color: 'from-blue-400 to-blue-600' },
                  { icon: Github, color: 'from-gray-600 to-gray-800' },
                  { icon: Linkedin, color: 'from-blue-600 to-blue-800' }
                ].map((social, index) => {
                  const Icon = social.icon
                  return (
                    <motion.a
                      key={index}
                      href="#"
                      className={`w-12 h-12 rounded-xl bg-gradient-to-br ${social.color} flex items-center justify-center text-white shadow-lg hover:shadow-xl transition-all duration-300`}
                      whileHover={{ scale: 1.1, y: -2 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Icon className="w-5 h-5" />
                    </motion.a>
                  )
                })}
              </div>
            </div>

            {/* Enhanced Links */}
            <div>
              <h3 className={`font-bold text-lg mb-6 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                Product
              </h3>
              <div className="space-y-4">
                {['Features', 'Pricing', 'Security', 'API'].map((link, index) => (
                  <motion.a
                    key={link}
                    href="#"
                    className={`block font-medium transition-all duration-300 ${
                      isDarkMode ? 'text-white/70 hover:text-white' : 'text-gray-600 hover:text-gray-900'
                    } hover:translate-x-2`}
                    whileHover={{ x: 5 }}
                  >
                    {link}
                  </motion.a>
                ))}
              </div>
            </div>

            {/* Enhanced Company */}
            <div>
              <h3 className={`font-bold text-lg mb-6 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                Company
              </h3>
              <div className="space-y-4">
                {['About', 'Blog', 'Careers', 'Contact'].map((link, index) => (
                  <motion.a
                    key={link}
                    href="#"
                    className={`block font-medium transition-all duration-300 ${
                      isDarkMode ? 'text-white/70 hover:text-white' : 'text-gray-600 hover:text-gray-900'
                    } hover:translate-x-2`}
                    whileHover={{ x: 5 }}
                  >
                    {link}
                  </motion.a>
                ))}
              </div>
            </div>
          </div>

          <motion.div
            className={`border-t ${isDarkMode ? 'border-purple-500/20' : 'border-purple-200/50'} mt-12 pt-8`}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 1, delay: 0.5 }}
          >
            <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
              <p className={`${isDarkMode ? 'text-white/70' : 'text-gray-600'} font-medium`}>
                © 2024 SplitWise. Made with <Heart className="w-4 h-4 inline text-red-500 mx-1" /> for better expense sharing.
              </p>
              <div className="flex space-x-6">
                <a href="#" className={`${isDarkMode ? 'text-white/70 hover:text-white' : 'text-gray-600 hover:text-gray-900'} transition-colors font-medium`}>
                  Privacy
                </a>
                <a href="#" className={`${isDarkMode ? 'text-white/70 hover:text-white' : 'text-gray-600 hover:text-gray-900'} transition-colors font-medium`}>
                  Terms
                </a>
                <a href="#" className={`${isDarkMode ? 'text-white/70 hover:text-white' : 'text-gray-600 hover:text-gray-900'} transition-colors font-medium`}>
                  Support
                </a>
              </div>
            </div>
          </motion.div>
        </div>
      </footer>
    </div>
  )
}

export default Home
