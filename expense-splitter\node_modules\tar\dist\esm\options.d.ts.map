{"version": 3, "file": "options.d.ts", "sourceRoot": "", "sources": ["../../src/options.ts"], "names": [], "mappings": ";AAEA,OAAO,EAAE,KAAK,WAAW,EAAE,KAAK,WAAW,EAAE,MAAM,UAAU,CAAA;AAC7D,OAAO,EAAE,KAAK,KAAK,EAAE,MAAM,SAAS,CAAA;AACpC,OAAO,EAAE,KAAK,SAAS,EAAE,MAAM,iBAAiB,CAAA;AAChD,OAAO,EAAE,KAAK,QAAQ,EAAE,MAAM,kBAAkB,CAAA;AAChD,OAAO,EAAE,UAAU,EAAE,MAAM,kBAAkB,CAAA;AA2B7C;;;;;;;GAOG;AACH,MAAM,WAAW,UAAU;IAIzB;;;OAGG;IACH,IAAI,CAAC,EAAE,OAAO,CAAA;IAEd;;;;;OAKG;IACH,IAAI,CAAC,EAAE,MAAM,CAAA;IAEb;;OAEG;IACH,MAAM,CAAC,EAAE,OAAO,CAAA;IAEhB;;OAEG;IACH,GAAG,CAAC,EAAE,MAAM,CAAA;IAEZ;;;;;;;;;OASG;IACH,IAAI,CAAC,EAAE,OAAO,GAAG,WAAW,CAAA;IAE5B;;;;;;;;;;;;;;OAcG;IACH,aAAa,CAAC,EAAE,OAAO,CAAA;IAEvB;;;;;;;OAOG;IACH,OAAO,CAAC,EAAE,OAAO,CAAA;IAEjB;;;;;;;;;;;;;;OAcG;IACH,MAAM,CAAC,EAAE,OAAO,GAAG,WAAW,CAAA;IAE9B;;;;OAIG;IACH,MAAM,CAAC,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,GAAG,SAAS,KAAK,OAAO,CAAA;IAE5D;;;;;OAKG;IACH,MAAM,CAAC,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,KAAK,GAAG,CAAA;IAK/D;;;;;OAKG;IACH,MAAM,CAAC,EAAE,OAAO,CAAA;IAEhB;;;;;;OAMG;IACH,KAAK,CAAC,EAAE,MAAM,CAAA;IAEd;;;OAGG;IACH,KAAK,CAAC,EAAE,OAAO,CAAA;IAEf;;OAEG;IACH,IAAI,CAAC,EAAE,OAAO,CAAA;IAEd;;;;;;;;;OASG;IACH,aAAa,CAAC,EAAE,OAAO,CAAA;IAEvB;;;;OAIG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAA;IAEjB;;;;;;;OAOG;IACH,GAAG,CAAC,EAAE,MAAM,CAAA;IAEZ;;;;;;;OAOG;IACH,GAAG,CAAC,EAAE,MAAM,CAAA;IAEZ;;;;;;;;;;;;;OAaG;IACH,SAAS,CAAC,EAAE,CAAC,KAAK,EAAE,SAAS,KAAK,GAAG,CAAA;IAErC;;;;;;;;;;;;;;;;;OAiBG;IACH,KAAK,CAAC,EAAE,OAAO,CAAA;IAEf;;;;;;;;;;;;;;OAcG;IACH,YAAY,CAAC,EAAE,MAAM,CAAA;IAKrB;;;;;;;;;;OAUG;IACH,QAAQ,CAAC,EAAE,OAAO,CAAA;IAElB;;;OAGG;IACH,YAAY,CAAC,EAAE,CAAC,KAAK,EAAE,UAAU,KAAK,GAAG,CAAA;IAEzC;;;;;;OAMG;IACH,WAAW,CAAC,EAAE,CAAC,KAAK,EAAE,SAAS,KAAK,GAAG,CAAA;IAEvC;;OAEG;IACH,MAAM,CAAC,EAAE,OAAO,CAAA;IAEhB;;;;;;;;;;;OAWG;IACH,QAAQ,CAAC,EAAE,OAAO,CAAA;IAElB;;;;OAIG;IACH,YAAY,CAAC,EAAE,OAAO,CAAA;IAEtB;;;;OAIG;IACH,KAAK,CAAC,EAAE,OAAO,CAAA;IAEf;;;;;;;;;OASG;IACH,KAAK,CAAC,EAAE,IAAI,CAAA;IAEZ;;OAEG;IACH,MAAM,CAAC,EAAE,MAAM,CAAA;IAEf;;;;;;OAMG;IACH,IAAI,CAAC,EAAE,MAAM,CAAA;IAKb;;;;OAIG;IACH,UAAU,CAAC,EAAE,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;IAE9B;;;;OAIG;IACH,WAAW,CAAC,EAAE,MAAM,CAAA;IAEpB;;;;OAIG;IACH,KAAK,CAAC,EAAE,MAAM,CAAA;IAEd;;;;;OAKG;IACH,KAAK,CAAC,EAAE,MAAM,CAAA;IAEd;;;;OAIG;IACH,KAAK,CAAC,EAAE,MAAM,CAAA;IAEd;;;;OAIG;IACH,QAAQ,CAAC,EAAE,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;IAC/B;;;;OAIG;IACH,gBAAgB,CAAC,EAAE,MAAM,CAAA;IAEzB;;;;;;OAMG;IACH,SAAS,CAAC,EAAE,GAAG,CAAC,YAAY,EAAE,MAAM,CAAC,CAAA;IAErC;;;;OAIG;IACH,YAAY,CAAC,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,CAAA;IAEpC;;;;OAIG;IACH,SAAS,CAAC,EAAE,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;IAE9B;;;;;;OAMG;IACH,IAAI,CAAC,EAAE,MAAM,CAAA;IAEb;;;;;;;;;;;;OAYG;IACH,KAAK,CAAC,EAAE,OAAO,CAAA;IAEf;;;;;;OAMG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAA;IAEjB;;;;;;;OAOG;IACH,MAAM,CAAC,EAAE,MAAM,IAAI,CAAA;IAEnB;;;OAGG;IACH,UAAU,CAAC,EAAE,OAAO,CAAA;IAEpB;;;;OAIG;IACH,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,SAAS,KAAK,GAAG,CAAA;CACpC;AAED,MAAM,MAAM,cAAc,GAAG,UAAU,GAAG;IAAE,IAAI,EAAE,IAAI,CAAA;CAAE,CAAA;AACxD,MAAM,MAAM,eAAe,GAAG,UAAU,GAAG;IAAE,IAAI,CAAC,EAAE,KAAK,CAAA;CAAE,CAAA;AAC3D,MAAM,MAAM,cAAc,GAAG,UAAU,GAAG;IAAE,IAAI,EAAE,MAAM,CAAA;CAAE,CAAA;AAC1D,MAAM,MAAM,gBAAgB,GAAG,UAAU,GAAG;IAAE,IAAI,CAAC,EAAE,SAAS,CAAA;CAAE,CAAA;AAChE,MAAM,MAAM,kBAAkB,GAAG,cAAc,GAAG,cAAc,CAAA;AAChE,MAAM,MAAM,mBAAmB,GAAG,eAAe,GAAG,cAAc,CAAA;AAClE,MAAM,MAAM,oBAAoB,GAAG,cAAc,GAAG,gBAAgB,CAAA;AACpE,MAAM,MAAM,qBAAqB,GAAG,eAAe,GAAG,gBAAgB,CAAA;AAEtE,MAAM,MAAM,YAAY,GAAG,GAAG,MAAM,IAAI,MAAM,EAAE,CAAA;AAEhD,MAAM,WAAW,qBAAsB,SAAQ,UAAU;IACvD;;OAEG;IACH,CAAC,CAAC,EAAE,UAAU,CAAC,KAAK,CAAC,CAAA;IACrB;;;;;OAKG;IACH,CAAC,CAAC,EAAE,UAAU,CAAC,MAAM,CAAC,CAAA;IACtB;;;;;;;;;OASG;IACH,CAAC,CAAC,EAAE,UAAU,CAAC,MAAM,CAAC,CAAA;IACtB;;;;;;;;;;;;;;OAcG;IACH,CAAC,CAAC,EAAE,UAAU,CAAC,eAAe,CAAC,CAAA;IAC/B;;;;;OAKG;IACH,CAAC,CAAC,EAAE,UAAU,CAAC,QAAQ,CAAC,CAAA;IACxB;;;;OAIG;IACH,kBAAkB,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAA;IACxC;;;;OAIG;IACH,eAAe,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAA;IACrC;;;OAGG;IACH,YAAY,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAA;IAClC;;;OAGG;IACH,SAAS,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAA;IAC/B;;;OAGG;IACH,kBAAkB,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAA;IACxC;;;OAGG;IACH,cAAc,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAA;IACpC;;OAEG;IACH,CAAC,CAAC,EAAE,UAAU,CAAC,MAAM,CAAC,CAAA;IACtB;;OAEG;IACH,eAAe,CAAC,EAAE,UAAU,CAAC,MAAM,CAAC,CAAA;IACpC;;OAEG;IACH,YAAY,CAAC,EAAE,UAAU,CAAC,MAAM,CAAC,CAAA;IACjC;;;;;;;OAOG;IACH,CAAC,CAAC,EAAE,UAAU,CAAC,SAAS,CAAC,CAAA;IACzB;;;;;;;OAOG;IACH,UAAU,CAAC,EAAE,UAAU,CAAC,SAAS,CAAC,CAAA;IAClC;;;;;;;;;OASG;IACH,CAAC,CAAC,EAAE,UAAU,CAAC,eAAe,CAAC,CAAA;IAC/B;;OAEG;IACH,CAAC,CAAC,EAAE,UAAU,CAAC,QAAQ,CAAC,CAAA;IACxB;;OAEG;IACH,CAAC,CAAC,EAAE,UAAU,CAAC,QAAQ,CAAC,CAAA;IAExB;;;;;OAKG;IACH,OAAO,CAAC,EAAE,OAAO,CAAA;CAClB;AAED,MAAM,MAAM,yBAAyB,GAAG,qBAAqB,GAAG;IAC9D,IAAI,EAAE,IAAI,CAAA;CACX,CAAA;AACD,MAAM,MAAM,0BAA0B,GAAG,qBAAqB,GAAG;IAC/D,IAAI,CAAC,EAAE,KAAK,CAAA;CACb,CAAA;AACD,MAAM,MAAM,yBAAyB,GACjC,CAAC,qBAAqB,GAAG;IACvB,IAAI,EAAE,MAAM,CAAA;CACb,CAAC,GACF,CAAC,qBAAqB,GAAG;IAAE,CAAC,EAAE,MAAM,CAAA;CAAE,CAAC,CAAA;AAC3C,MAAM,MAAM,6BAA6B,GACvC,yBAAyB,GAAG,yBAAyB,CAAA;AACvD,MAAM,MAAM,8BAA8B,GACxC,0BAA0B,GAAG,yBAAyB,CAAA;AAExD,MAAM,MAAM,2BAA2B,GAAG,qBAAqB,GAAG;IAChE,CAAC,CAAC,EAAE,SAAS,CAAA;IACb,IAAI,CAAC,EAAE,SAAS,CAAA;CACjB,CAAA;AAED,MAAM,MAAM,+BAA+B,GACzC,yBAAyB,GAAG,2BAA2B,CAAA;AACzD,MAAM,MAAM,gCAAgC,GAC1C,0BAA0B,GAAG,2BAA2B,CAAA;AAE1D,eAAO,MAAM,UAAU,4BAClB,CAAC;UA/K4C,IAAI;;UAEJ,MAAM;CA8KF,CAAA;AACtD,eAAO,MAAM,WAAW,4BACnB,CAAC;;;UAhL4C,MAAM;CAiLF,CAAA;AACtD,eAAO,MAAM,YAAY,4BACpB,CAAC;UArL4C,IAAI;;WAGD,SAAS;CAmLP,CAAA;AACvD,eAAO,MAAM,aAAa,4BACrB,CAAC;;;WArL+C,SAAS;CAsLP,CAAA;AACvD,eAAO,MAAM,MAAM,4BACd,CAAC;UA3L4C,IAAI;CA4LhB,CAAA;AACtC,eAAO,MAAM,OAAO,4BACf,CAAC;;CACgC,CAAA;AACtC,eAAO,MAAM,MAAM,4BACd,CAAC;UA/L4C,MAAM;CAgMlB,CAAA;AACtC,eAAO,MAAM,QAAQ,4BAChB,CAAC;WAjM+C,SAAS;CAkMvB,CAAA;AAUvC,eAAO,MAAM,OAAO,SACb,qBAAqB,KACzB,UAiBF,CAAA"}