import { motion } from 'framer-motion'
import { useState } from 'react'
import {
  DollarSign,
  Calendar,
  Tag,
  Users,
  PlusCircle,
  X,
  Check,
  AlertCircle,
  Receipt,
  FileText,
  UserPlus,
  Save,
  ArrowLeft,
  Sparkles
} from 'lucide-react'
import { useNavigate } from 'react-router-dom'
import AppNavbar from '../components/ui/AppNavbar'

const categories = [
  { id: 'food', name: 'Food & Dining', color: 'from-orange-400 to-red-500', emoji: '🍽️' },
  { id: 'transport', name: 'Transport', color: 'from-blue-400 to-indigo-500', emoji: '🚗' },
  { id: 'entertainment', name: 'Entertainment', color: 'from-purple-400 to-pink-500', emoji: '🎬' },
  { id: 'shopping', name: 'Shopping', color: 'from-green-400 to-emerald-500', emoji: '🛍️' },
  { id: 'utilities', name: 'Utilities', color: 'from-yellow-400 to-orange-500', emoji: '⚡' },
  { id: 'other', name: 'Other', color: 'from-gray-400 to-slate-500', emoji: '📝' }
]

function AddExpense() {
  const navigate = useNavigate()
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    amount: '',
    date: new Date().toISOString().split('T')[0],
    category: '',
    participants: []
  })
  const [newParticipant, setNewParticipant] = useState('')
  const [errors, setErrors] = useState({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const addParticipant = () => {
    if (newParticipant.trim() && !formData.participants.includes(newParticipant.trim())) {
      setFormData(prev => ({
        ...prev,
        participants: [...prev.participants, newParticipant.trim()]
      }))
      setNewParticipant('')
    }
  }

  const removeParticipant = (participant) => {
    setFormData(prev => ({
      ...prev,
      participants: prev.participants.filter(p => p !== participant)
    }))
  }

  const validateForm = () => {
    const newErrors = {}
    
    if (!formData.title.trim()) newErrors.title = 'Title is required'
    if (!formData.amount || parseFloat(formData.amount) <= 0) newErrors.amount = 'Valid amount is required'
    if (!formData.category) newErrors.category = 'Category is required'
    if (formData.participants.length === 0) newErrors.participants = 'At least one participant is required'
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!validateForm()) return
    
    setIsSubmitting(true)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    setIsSubmitting(false)
    navigate('/dashboard')
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      <AppNavbar />
      <div className="pt-8 pb-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Enhanced Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="mb-12"
          >
            <div className="flex items-center space-x-4 mb-6">
              <motion.button
                onClick={() => navigate('/dashboard')}
                whileHover={{ scale: 1.05, x: -2 }}
                whileTap={{ scale: 0.95 }}
                className="p-3 rounded-xl bg-white/80 backdrop-blur-sm border border-gray-200/50 hover:bg-white hover:shadow-md transition-all duration-300"
              >
                <ArrowLeft className="w-5 h-5 text-gray-600" />
              </motion.button>
              <div>
                <motion.h1
                  className="text-4xl lg:text-5xl font-black text-gray-900 mb-2"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.1 }}
                >
                  Add New <span className="bg-gradient-to-r from-pink-500 via-purple-500 to-indigo-500 bg-clip-text text-transparent">Expense</span>
                </motion.h1>
                <motion.p
                  className="text-xl text-gray-600 font-medium"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                >
                  Split a new expense with your <span className="text-purple-600 font-semibold">friends</span> ✨
                </motion.p>
              </div>
            </div>
          </motion.div>

          {/* Enhanced Form */}
          <motion.form
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            onSubmit={handleSubmit}
            className="bg-white/80 backdrop-blur-xl rounded-3xl p-8 lg:p-12 shadow-xl shadow-blue-100/20 border border-white/20 space-y-8"
          >
          {/* Enhanced Title and Description */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="space-y-3">
              <label className="flex items-center space-x-2 text-lg font-semibold text-gray-800">
                <Receipt className="w-5 h-5 text-purple-600" />
                <span>Expense Title *</span>
              </label>
              <div className="relative">
                <input
                  type="text"
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  className={`w-full px-6 py-4 rounded-2xl bg-white/60 backdrop-blur-sm border-2 transition-all duration-300 focus:outline-none focus:ring-0 text-lg font-medium ${
                    errors.title
                      ? 'border-red-400 focus:border-red-500 bg-red-50/50'
                      : 'border-gray-200/50 focus:border-purple-400 focus:bg-white/80'
                  }`}
                  placeholder="e.g., Dinner at Italian Restaurant"
                />
                {errors.title && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="flex items-center space-x-2 mt-3 text-red-600 text-sm font-medium"
                  >
                    <AlertCircle size={16} />
                    <span>{errors.title}</span>
                  </motion.div>
                )}
              </div>
            </div>

            <div className="space-y-3">
              <label className="flex items-center space-x-2 text-lg font-semibold text-gray-800">
                <FileText className="w-5 h-5 text-blue-600" />
                <span>Description</span>
              </label>
              <input
                type="text"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                className="w-full px-6 py-4 rounded-2xl bg-white/60 backdrop-blur-sm border-2 border-gray-200/50 focus:border-blue-400 focus:bg-white/80 transition-all duration-300 focus:outline-none focus:ring-0 text-lg font-medium"
                placeholder="Optional description"
              />
            </div>
          </div>

          {/* Enhanced Amount and Date */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="space-y-3">
              <label className="flex items-center space-x-2 text-lg font-semibold text-gray-800">
                <DollarSign className="w-5 h-5 text-green-600" />
                <span>Amount *</span>
              </label>
              <div className="relative">
                <div className="absolute left-6 top-1/2 transform -translate-y-1/2 text-green-600">
                  <DollarSign size={20} />
                </div>
                <input
                  type="number"
                  step="0.01"
                  value={formData.amount}
                  onChange={(e) => handleInputChange('amount', e.target.value)}
                  className={`w-full pl-16 pr-6 py-4 rounded-2xl bg-white/60 backdrop-blur-sm border-2 transition-all duration-300 focus:outline-none focus:ring-0 text-lg font-bold ${
                    errors.amount
                      ? 'border-red-400 focus:border-red-500 bg-red-50/50'
                      : 'border-gray-200/50 focus:border-green-400 focus:bg-white/80'
                  }`}
                  placeholder="0.00"
                />
                {errors.amount && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="flex items-center space-x-2 mt-3 text-red-600 text-sm font-medium"
                  >
                    <AlertCircle size={16} />
                    <span>{errors.amount}</span>
                  </motion.div>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-slate-700 dark:text-slate-300">
                Date
              </label>
              <div className="relative">
                <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400">
                  <Calendar size={18} />
                </div>
                <input
                  type="date"
                  value={formData.date}
                  onChange={(e) => handleInputChange('date', e.target.value)}
                  className="w-full pl-10 pr-4 py-3 rounded-xl glass-strong border-2 border-transparent focus:border-primary-400 transition-all duration-300 focus:outline-none focus:ring-0"
                />
              </div>
            </div>
          </div>

          {/* Category Selection */}
          <div className="space-y-4">
            <label className="text-sm font-medium text-slate-700 dark:text-slate-300">
              Category *
            </label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {categories.map((category) => (
                <motion.button
                  key={category.id}
                  type="button"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => handleInputChange('category', category.id)}
                  className={`p-4 rounded-xl transition-all duration-300 ${
                    formData.category === category.id
                      ? `bg-gradient-to-r ${category.color} text-white shadow-lg`
                      : 'glass-strong hover:bg-white/30'
                  }`}
                >
                  <div className="text-2xl mb-2">{category.emoji}</div>
                  <div className="text-sm font-medium">{category.name}</div>
                </motion.button>
              ))}
            </div>
            {errors.category && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="flex items-center space-x-1 text-red-500 text-sm"
              >
                <AlertCircle size={14} />
                <span>{errors.category}</span>
              </motion.div>
            )}
          </div>

          {/* Participants */}
          <div className="space-y-4">
            <label className="text-sm font-medium text-slate-700 dark:text-slate-300">
              Split with *
            </label>
            
            {/* Add Participant */}
            <div className="flex space-x-3">
              <div className="flex-1 relative">
                <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400">
                  <Users size={18} />
                </div>
                <input
                  type="text"
                  value={newParticipant}
                  onChange={(e) => setNewParticipant(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addParticipant())}
                  className="w-full pl-10 pr-4 py-3 rounded-xl glass-strong border-2 border-transparent focus:border-primary-400 transition-all duration-300 focus:outline-none focus:ring-0"
                  placeholder="Add participant name"
                />
              </div>
              <motion.button
                type="button"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={addParticipant}
                className="px-6 py-3 bg-gradient-to-r from-primary-500 to-primary-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <Plus size={18} />
              </motion.button>
            </div>

            {/* Participants List */}
            {formData.participants.length > 0 && (
              <div className="space-y-2">
                <div className="text-sm text-slate-600 dark:text-slate-400">
                  Participants ({formData.participants.length})
                </div>
                <div className="flex flex-wrap gap-2">
                  {formData.participants.map((participant, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.8 }}
                      className="flex items-center space-x-2 px-3 py-2 glass-strong rounded-lg"
                    >
                      <span className="text-sm font-medium">{participant}</span>
                      <button
                        type="button"
                        onClick={() => removeParticipant(participant)}
                        className="text-slate-400 hover:text-red-500 transition-colors duration-200"
                      >
                        <X size={14} />
                      </button>
                    </motion.div>
                  ))}
                </div>
              </div>
            )}
            
            {errors.participants && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="flex items-center space-x-1 text-red-500 text-sm"
              >
                <AlertCircle size={14} />
                <span>{errors.participants}</span>
              </motion.div>
            )}
          </div>

          {/* Submit Button */}
          <div className="pt-6">
            <motion.button
              type="submit"
              disabled={isSubmitting}
              whileHover={{ scale: isSubmitting ? 1 : 1.02 }}
              whileTap={{ scale: isSubmitting ? 1 : 0.98 }}
              className={`w-full py-4 rounded-xl font-semibold text-lg transition-all duration-300 flex items-center justify-center space-x-2 ${
                isSubmitting
                  ? 'bg-slate-400 cursor-not-allowed'
                  : 'bg-gradient-to-r from-primary-500 to-accent-500 hover:from-primary-600 hover:to-accent-600 shadow-lg hover:shadow-xl'
              } text-white`}
            >
              {isSubmitting ? (
                <>
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  <span>Creating Expense...</span>
                </>
              ) : (
                <>
                  <Check size={20} />
                  <span>Create Expense</span>
                </>
              )}
            </motion.button>
          </div>
        </motion.form>
      </div>
      </div>
    </div>
  )
}

export default AddExpense
