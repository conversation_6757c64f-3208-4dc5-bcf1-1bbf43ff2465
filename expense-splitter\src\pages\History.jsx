import { motion } from 'framer-motion'
import { useState } from 'react'
import { 
  Search, 
  Filter, 
  Calendar,
  TrendingUp,
  Download,
  Eye,
  MoreHorizontal
} from 'lucide-react'
import ExpenseCard from '../components/ui/ExpenseCard'

// Extended mock data for history
const mockHistoryExpenses = [
  {
    id: 1,
    title: 'Dinner at Italian Restaurant',
    description: 'Team dinner after project completion',
    amount: 240.50,
    date: '2024-01-15',
    category: 'food',
    participants: [
      { name: '<PERSON>', amount: 60.13 },
      { name: '<PERSON>', amount: 60.13 },
      { name: '<PERSON>', amount: 60.12 },
      { name: '<PERSON>', amount: 60.12 }
    ],
    yourShare: 60.13,
    status: 'settled'
  },
  {
    id: 2,
    title: 'Uber to Airport',
    description: 'Shared ride for vacation trip',
    amount: 45.00,
    date: '2024-01-14',
    category: 'transport',
    participants: [
      { name: 'You', amount: 22.50 },
      { name: '<PERSON>', amount: 22.50 }
    ],
    yourShare: 22.50,
    status: 'pending'
  },
  {
    id: 3,
    title: 'Movie Night',
    description: 'Tickets and snacks for group movie',
    amount: 85.75,
    date: '2024-01-12',
    category: 'entertainment',
    participants: [
      { name: '<PERSON>', amount: 28.58 },
      { name: '<PERSON>', amount: 28.58 },
      { name: '<PERSON>', amount: 28.59 }
    ],
    yourShare: 28.58,
    status: 'settled'
  },
  {
    id: 4,
    title: 'Grocery Shopping',
    description: 'Weekly groceries for the house',
    amount: 156.80,
    date: '2024-01-10',
    category: 'shopping',
    participants: [
      { name: 'You', amount: 52.27 },
      { name: 'Alex', amount: 52.27 },
      { name: 'Jordan', amount: 52.26 }
    ],
    yourShare: 52.27,
    status: 'settled'
  },
  {
    id: 5,
    title: 'Concert Tickets',
    description: 'Rock concert downtown',
    amount: 320.00,
    date: '2024-01-08',
    category: 'entertainment',
    participants: [
      { name: 'You', amount: 80.00 },
      { name: 'Emma', amount: 80.00 },
      { name: 'David', amount: 80.00 },
      { name: 'Sophie', amount: 80.00 }
    ],
    yourShare: 80.00,
    status: 'pending'
  },
  {
    id: 6,
    title: 'Gas Station',
    description: 'Road trip fuel',
    amount: 75.25,
    date: '2024-01-05',
    category: 'transport',
    participants: [
      { name: 'You', amount: 37.63 },
      { name: 'Chris', amount: 37.62 }
    ],
    yourShare: 37.63,
    status: 'settled'
  }
]

const filterOptions = [
  { value: 'all', label: 'All Expenses' },
  { value: 'pending', label: 'Pending' },
  { value: 'settled', label: 'Settled' },
  { value: 'food', label: 'Food & Dining' },
  { value: 'transport', label: 'Transport' },
  { value: 'entertainment', label: 'Entertainment' },
  { value: 'shopping', label: 'Shopping' }
]

function History() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedFilter, setSelectedFilter] = useState('all')
  const [showFilters, setShowFilters] = useState(false)
  const [expenses, setExpenses] = useState(mockHistoryExpenses)

  const filteredExpenses = expenses.filter(expense => {
    const matchesSearch = expense.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         expense.description.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesFilter = selectedFilter === 'all' || 
                         expense.status === selectedFilter ||
                         expense.category === selectedFilter
    
    return matchesSearch && matchesFilter
  })

  const totalAmount = filteredExpenses.reduce((sum, expense) => sum + expense.amount, 0)
  const yourTotalShare = filteredExpenses.reduce((sum, expense) => sum + expense.yourShare, 0)

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  return (
    <div className="min-h-screen pt-24 pb-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
              <h1 className="text-3xl md:text-4xl font-bold gradient-text mb-2">
                Expense History
              </h1>
              <p className="text-slate-600 dark:text-slate-400">
                View and manage all your past expenses
              </p>
            </div>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="mt-4 md:mt-0 px-6 py-3 glass-strong rounded-xl font-semibold hover:bg-white/30 transition-all duration-300 flex items-center space-x-2"
            >
              <Download size={20} />
              <span>Export</span>
            </motion.button>
          </div>
        </motion.div>

        {/* Summary Cards */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8"
        >
          <div className="glass rounded-2xl p-6">
            <div className="flex items-center space-x-3 mb-2">
              <div className="p-2 rounded-lg bg-gradient-to-r from-blue-400 to-indigo-500 text-white">
                <TrendingUp size={20} />
              </div>
              <div>
                <div className="text-sm text-slate-600 dark:text-slate-400">Total Expenses</div>
                <div className="text-xl font-bold text-slate-800 dark:text-slate-200">
                  {formatCurrency(totalAmount)}
                </div>
              </div>
            </div>
          </div>

          <div className="glass rounded-2xl p-6">
            <div className="flex items-center space-x-3 mb-2">
              <div className="p-2 rounded-lg bg-gradient-to-r from-green-400 to-emerald-500 text-white">
                <Eye size={20} />
              </div>
              <div>
                <div className="text-sm text-slate-600 dark:text-slate-400">Your Share</div>
                <div className="text-xl font-bold text-slate-800 dark:text-slate-200">
                  {formatCurrency(yourTotalShare)}
                </div>
              </div>
            </div>
          </div>

          <div className="glass rounded-2xl p-6">
            <div className="flex items-center space-x-3 mb-2">
              <div className="p-2 rounded-lg bg-gradient-to-r from-purple-400 to-pink-500 text-white">
                <Calendar size={20} />
              </div>
              <div>
                <div className="text-sm text-slate-600 dark:text-slate-400">Total Transactions</div>
                <div className="text-xl font-bold text-slate-800 dark:text-slate-200">
                  {filteredExpenses.length}
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Search and Filters */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="glass rounded-2xl p-6 mb-8"
        >
          <div className="flex flex-col md:flex-row gap-4">
            {/* Search */}
            <div className="flex-1 relative">
              <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400">
                <Search size={18} />
              </div>
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 rounded-xl glass-strong border-2 border-transparent focus:border-primary-400 transition-all duration-300 focus:outline-none focus:ring-0"
                placeholder="Search expenses..."
              />
            </div>

            {/* Filter Button */}
            <div className="relative">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setShowFilters(!showFilters)}
                className="px-6 py-3 glass-strong rounded-xl font-medium hover:bg-white/30 transition-all duration-300 flex items-center space-x-2"
              >
                <Filter size={18} />
                <span>Filter</span>
              </motion.button>

              {/* Filter Dropdown */}
              {showFilters && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.95, y: -10 }}
                  animate={{ opacity: 1, scale: 1, y: 0 }}
                  exit={{ opacity: 0, scale: 0.95, y: -10 }}
                  className="absolute right-0 top-full mt-2 w-48 glass-strong rounded-xl shadow-xl z-20"
                >
                  <div className="py-2">
                    {filterOptions.map((option) => (
                      <button
                        key={option.value}
                        onClick={() => {
                          setSelectedFilter(option.value)
                          setShowFilters(false)
                        }}
                        className={`w-full px-4 py-2 text-left text-sm transition-colors duration-200 ${
                          selectedFilter === option.value
                            ? 'bg-primary-500/20 text-primary-600 dark:text-primary-400'
                            : 'hover:bg-white/20'
                        }`}
                      >
                        {option.label}
                      </button>
                    ))}
                  </div>
                </motion.div>
              )}
            </div>
          </div>

          {/* Active Filter Display */}
          {selectedFilter !== 'all' && (
            <div className="mt-4 flex items-center space-x-2">
              <span className="text-sm text-slate-600 dark:text-slate-400">Active filter:</span>
              <div className="px-3 py-1 bg-primary-500/20 text-primary-600 dark:text-primary-400 rounded-full text-sm font-medium">
                {filterOptions.find(f => f.value === selectedFilter)?.label}
              </div>
              <button
                onClick={() => setSelectedFilter('all')}
                className="text-slate-400 hover:text-slate-600 dark:hover:text-slate-300 transition-colors duration-200"
              >
                <MoreHorizontal size={16} />
              </button>
            </div>
          )}
        </motion.div>

        {/* Expenses Grid */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.3 }}
        >
          {filteredExpenses.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredExpenses.map((expense, index) => (
                <motion.div
                  key={expense.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: 0.4 + index * 0.05 }}
                >
                  <ExpenseCard 
                    expense={expense}
                    onEdit={(expense) => console.log('Edit:', expense)}
                    onDelete={(expense) => console.log('Delete:', expense)}
                    onView={(expense) => console.log('View:', expense)}
                  />
                </motion.div>
              ))}
            </div>
          ) : (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-center py-16"
            >
              <div className="glass rounded-2xl p-12 max-w-md mx-auto">
                <div className="text-6xl mb-4">📊</div>
                <h3 className="text-xl font-semibold text-slate-800 dark:text-slate-200 mb-2">
                  No expenses found
                </h3>
                <p className="text-slate-600 dark:text-slate-400">
                  Try adjusting your search or filter criteria
                </p>
              </div>
            </motion.div>
          )}
        </motion.div>
      </div>
    </div>
  )
}

export default History
