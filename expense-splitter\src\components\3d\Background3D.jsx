import { Canvas, useFrame } from '@react-three/fiber'
import { Sphere, MeshDistortMaterial } from '@react-three/drei'
import { useRef } from 'react'
import * as THREE from 'three'

function AnimatedSphere({ position, color, scale = 1 }) {
  const meshRef = useRef()
  
  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.x = state.clock.elapsedTime * 0.2
      meshRef.current.rotation.y = state.clock.elapsedTime * 0.3
      meshRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime) * 0.1
    }
  })

  return (
    <Sphere ref={meshRef} args={[1, 64, 64]} position={position} scale={scale}>
      <MeshDistortMaterial
        color={color}
        attach="material"
        distort={0.4}
        speed={2}
        roughness={0.1}
        metalness={0.8}
        transparent
        opacity={0.6}
      />
    </Sphere>
  )
}

function FloatingGeometry({ position, geometry, color }) {
  const meshRef = useRef()
  
  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.x = state.clock.elapsedTime * 0.1
      meshRef.current.rotation.y = state.clock.elapsedTime * 0.15
      meshRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime * 0.8) * 0.2
    }
  })

  return (
    <mesh ref={meshRef} position={position}>
      {geometry}
      <meshStandardMaterial
        color={color}
        transparent
        opacity={0.3}
        roughness={0.2}
        metalness={0.9}
      />
    </mesh>
  )
}

function Background3D() {
  return (
    <div className="fixed inset-0 -z-10">
      <Canvas
        camera={{ position: [0, 0, 5], fov: 75 }}
        style={{ background: 'transparent' }}
      >
        {/* Lighting */}
        <ambientLight intensity={0.4} />
        <directionalLight position={[10, 10, 5]} intensity={1} />
        <pointLight position={[-10, -10, -5]} intensity={0.5} color="#22c55e" />
        <pointLight position={[10, -10, -5]} intensity={0.5} color="#d946ef" />
        
        {/* Animated Spheres */}
        <AnimatedSphere 
          position={[-3, 2, -2]} 
          color="#22c55e" 
          scale={0.8}
        />
        <AnimatedSphere 
          position={[3, -1, -3]} 
          color="#d946ef" 
          scale={1.2}
        />
        <AnimatedSphere 
          position={[0, 1, -4]} 
          color="#3b82f6" 
          scale={0.6}
        />
        
        {/* Floating Geometries */}
        <FloatingGeometry
          position={[-2, -2, -1]}
          geometry={<boxGeometry args={[0.5, 0.5, 0.5]} />}
          color="#f59e0b"
        />
        <FloatingGeometry
          position={[2, 2, -2]}
          geometry={<octahedronGeometry args={[0.4]} />}
          color="#ef4444"
        />
        <FloatingGeometry
          position={[1, -1, -1]}
          geometry={<tetrahedronGeometry args={[0.3]} />}
          color="#8b5cf6"
        />
      </Canvas>
    </div>
  )
}

export default Background3D
